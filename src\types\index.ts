export interface Chapter {
  id: string;
  title: string;
  arabicTitle: string;
  content: string;
  arabicContent: string;
  sections: Section[];
  exercises?: Exercise[];
  references?: Reference[];
}

export interface Section {
  id: string;
  title: string;
  arabicTitle: string;
  content: string;
  arabicContent: string;
  subsections?: Subsection[];
}

export interface Subsection {
  id: string;
  title: string;
  arabicTitle: string;
  content: string;
  arabicContent: string;
}

export interface Exercise {
  id: string;
  question: string;
  arabicQuestion: string;
  type: 'discussion' | 'practical' | 'research';
}

export interface Reference {
  id: string;
  citation: string;
  arabicCitation: string;
  type: 'journal' | 'book' | 'website' | 'patent';
}

export interface Lecture {
  id: string;
  title: string;
  arabicTitle: string;
  description: string;
  arabicDescription: string;
  duration: number;
  videoUrl?: string;
  slides?: string[];
  materials?: LectureMaterial[];
  quiz?: Quiz;
}

export interface LectureMaterial {
  id: string;
  title: string;
  arabicTitle: string;
  type: 'pdf' | 'video' | 'presentation' | 'document';
  url: string;
}

export interface Quiz {
  id: string;
  questions: QuizQuestion[];
}

export interface QuizQuestion {
  id: string;
  question: string;
  arabicQuestion: string;
  options: string[];
  arabicOptions: string[];
  correctAnswer: number;
  explanation: string;
  arabicExplanation: string;
}

export interface BookData {
  title: string;
  arabicTitle: string;
  chapters: Chapter[];
  lectures: Lecture[];
  metadata: {
    author: string;
    arabicAuthor: string;
    year: number;
    publisher: string;
    arabicPublisher: string;
    isbn: string;
    email: string;
    phones: string[];
    affiliation: string;
    arabicAffiliation: string;
  };
}

export type Language = 'ar' | 'en';