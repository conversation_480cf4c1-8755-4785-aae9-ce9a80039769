<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n-key="integration_showcase_title">Complete Integration Showcase - Medical Device Innovation Guide</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
    <link href="css/workspace-tools.css" rel="stylesheet">
    <link href="css/layout-fixes.css" rel="stylesheet">
    
    <style>
        .showcase-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0 3rem 0;
            margin-top: 0;
            position: relative;
            z-index: 1;
        }
        .feature-showcase {
            padding: 3rem 0;
            position: relative;
            z-index: 1;
        }
        .integration-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            transition: transform 0.2s ease;
            position: relative;
            z-index: 1;
        }
        .integration-card:hover {
            transform: translateY(-4px);
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .demo-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        .quick-demo {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
        }
    </style>
</head>
<body data-stage-key="integration_showcase">
    <!-- Showcase Header -->
    <div class="showcase-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3" data-i18n-key="integration_showcase_title">Complete Integration Showcase</h1>
                    <p class="lead mb-4" data-i18n-key="showcase_subtitle">
                        Experience the fully integrated Medical Device Innovation Guide with all advanced features, workspace tools, and bilingual capabilities.
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <span class="badge bg-light text-dark fs-6">
                            <i class="fas fa-check me-2"></i>Interactive Academic Book
                        </span>
                        <span class="badge bg-light text-dark fs-6">
                            <i class="fas fa-check me-2"></i>Workspace Tools
                        </span>
                        <span class="badge bg-light text-dark fs-6">
                            <i class="fas fa-check me-2"></i>Voice Narration
                        </span>
                        <span class="badge bg-light text-dark fs-6">
                            <i class="fas fa-check me-2"></i>Enhanced Navigation
                        </span>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-rocket fa-5x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Integration Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" data-i18n-number="15">15</div>
                <div class="stat-label">Integrated Pages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" data-i18n-number="9">9</div>
                <div class="stat-label">JavaScript Modules</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" data-i18n-number="7">7</div>
                <div class="stat-label">Interactive Workspace Tools</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" data-i18n-number="200">200+</div>
                <div class="stat-label">Translation Keys</div>
            </div>
        </div>

        <!-- Feature Showcase -->
        <section class="feature-showcase">
            <div class="row text-center mb-5">
                <div class="col-lg-8 mx-auto">
                    <h2 class="display-5 fw-bold" data-i18n-key="integrated_features">Integrated Features</h2>
                    <p class="lead text-muted" data-i18n-key="features_desc">
                        All features work seamlessly together to provide a comprehensive learning experience
                    </p>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4">
                    <div class="integration-card">
                        <div class="feature-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h4 data-i18n-key="interactive_book">Interactive Academic Book</h4>
                        <p data-i18n-key="book_integration_desc">
                            Fully integrated with voice narration, workspace tools, and enhanced navigation for a complete learning experience.
                        </p>
                        <a href="Interactive Academic Book Medical Device Innovation Guide.html" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>Open Book
                        </a>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="integration-card">
                        <div class="feature-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h4 data-i18n-key="workspace_tools">Workspace Tools</h4>
                        <p data-i18n-key="tools_integration_desc">
                            Interactive tools for stages 3-9 including process mapping, SWOT analysis, brainstorming, testing, and FMEA.
                        </p>
                        <a href="stage-03-bilingual.html" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>Try Tools
                        </a>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="integration-card">
                        <div class="feature-icon">
                            <i class="fas fa-volume-up"></i>
                        </div>
                        <h4 data-i18n-key="voice_narration">Voice Narration</h4>
                        <p data-i18n-key="voice_integration_desc">
                            Bilingual text-to-speech system with speed, pitch, and volume controls for both English and Arabic content.
                        </p>
                        <button class="btn btn-primary" onclick="demoVoiceNarration()">
                            <i class="fas fa-play me-2"></i>Demo Voice
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4">
                    <div class="integration-card">
                        <div class="feature-icon">
                            <i class="fas fa-compass"></i>
                        </div>
                        <h4 data-i18n-key="enhanced_navigation">Enhanced Navigation</h4>
                        <p data-i18n-key="nav_integration_desc">
                            Unified navigation with search (Ctrl+K), bookmarks (Ctrl+B), breadcrumbs, and floating action buttons.
                        </p>
                        <button class="btn btn-primary" onclick="demoNavigation()">
                            <i class="fas fa-search me-2"></i>Demo Search
                        </button>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="integration-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4 data-i18n-key="analytics_performance">Analytics & Performance</h4>
                        <p data-i18n-key="analytics_integration_desc">
                            Real-time analytics tracking language usage, progress, and performance with optimization features.
                        </p>
                        <button class="btn btn-primary" onclick="showAnalytics()">
                            <i class="fas fa-chart-line me-2"></i>View Analytics
                        </button>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="integration-card">
                        <div class="feature-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <h4 data-i18n-key="bilingual_system">Bilingual System</h4>
                        <p data-i18n-key="bilingual_integration_desc">
                            Complete English and Arabic support with RTL layout, cultural formatting, and voice narration.
                        </p>
                        <button class="btn btn-primary" id="languageToggle" onclick="toggleLanguageDemo()">
                            <i class="fas fa-globe me-2"></i>Switch Language
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Demo Section -->
        <div class="quick-demo">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3 data-i18n-key="quick_demo_title">Quick Demo</h3>
                    <p class="mb-3" data-i18n-key="quick_demo_desc">
                        Try these features right now to see the integration in action:
                    </p>
                    <div class="d-flex gap-2 flex-wrap">
                        <button class="btn btn-light" onclick="enhancedNav.toggleSearch()">
                            <i class="fas fa-search me-2"></i>Search (Ctrl+K)
                        </button>
                        <button class="btn btn-light" onclick="demoVoiceNarration()">
                            <i class="fas fa-volume-up me-2"></i>Voice Demo
                        </button>
                        <button class="btn btn-light" onclick="enhancedNav.toggleFAB()">
                            <i class="fas fa-rocket me-2"></i>Quick Actions
                        </button>
                        <button class="btn btn-light" onclick="showAnalyticsDashboard()">
                            <i class="fas fa-chart-pie me-2"></i>Analytics
                        </button>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-play-circle fa-4x opacity-75"></i>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="demo-section">
            <h3 data-i18n-key="explore_platform">Explore the Platform</h3>
            <p data-i18n-key="explore_desc">Visit different sections to experience the complete integration:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 data-i18n-key="main_sections">Main Sections</h5>
                    <div class="list-group">
                        <a href="index.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-home me-2"></i>Home Landing Page
                        </a>
                        <a href="Interactive Academic Book Medical Device Innovation Guide.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-book-open me-2"></i>Interactive Academic Book
                        </a>
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i>Learning Dashboard
                        </a>
                        <a href="advanced-demo.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-rocket me-2"></i>Advanced Demo
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5 data-i18n-key="workspace_stages">Workspace Stages</h5>
                    <div class="list-group">
                        <a href="stage-03-bilingual.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-cogs me-2"></i>Stage 3: Innovation Process
                        </a>
                        <a href="stage-04-bilingual.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-line me-2"></i>Stage 4: Understanding Opportunity
                        </a>
                        <a href="stage-05-bilingual.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-users me-2"></i>Stage 5: Needs Discovery
                        </a>
                        <a href="stage-06-bilingual.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-lightbulb me-2"></i>Stage 6: Idea Generation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/i18n.js"></script>
    <script src="js/i18n-advanced.js"></script>
    <script src="js/content-store.js"></script>
    <script src="js/ui-builder.js"></script>
    <script src="js/voice-narration.js"></script>
    <script src="js/analytics-performance.js"></script>
    <script src="js/workspace-tools.js"></script>
    <script src="js/enhanced-navigation.js"></script>
    <script src="js/layout-manager.js"></script>
    <script src="main.js"></script>
    
    <script>
        // Demo functions
        function demoVoiceNarration() {
            const demoText = document.querySelector('[data-i18n-key="showcase_subtitle"]');
            if (window.voiceNarration && demoText) {
                window.voiceNarration.speakElement(demoText);
            } else {
                alert('Voice narration system is loading...');
            }
        }
        
        function demoNavigation() {
            if (window.enhancedNav) {
                window.enhancedNav.toggleSearch();
            } else {
                alert('Enhanced navigation is loading...');
            }
        }
        
        function showAnalytics() {
            if (window.showAnalyticsDashboard) {
                window.showAnalyticsDashboard();
            } else {
                alert('Analytics dashboard will open here');
            }
        }
        
        function toggleLanguageDemo() {
            if (window.I18n) {
                const currentLang = window.I18n.getCurrentLanguage();
                const newLang = currentLang === 'en' ? 'ar' : 'en';
                window.I18n.setLanguage(newLang);
            }
        }
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            // Update language toggle button text
            window.addEventListener('languageChanged', function(event) {
                const button = document.getElementById('languageToggle');
                if (button) {
                    const lang = event.detail.language;
                    button.innerHTML = `<i class="fas fa-globe me-2"></i>${lang === 'en' ? 'العربية' : 'English'}`;
                }
            });
        });
    </script>
</body>
</html>
