/**
 * Medical Device Innovation Guide - Voice Narration System
 * Text-to-speech functionality for both English and Arabic content
 */

class VoiceNarration {
    constructor() {
        this.synthesis = window.speechSynthesis;
        this.voices = [];
        this.currentUtterance = null;
        this.isPlaying = false;
        this.currentLanguage = 'en';
        this.settings = {
            rate: 1.0,
            pitch: 1.0,
            volume: 1.0
        };
        
        this.init();
    }
    
    /**
     * Initialize the voice narration system
     */
    init() {
        if (!this.synthesis) {
            console.warn('Speech synthesis not supported in this browser');
            return;
        }
        
        // Load voices when they become available
        this.loadVoices();
        
        // Listen for voices changed event
        this.synthesis.addEventListener('voiceschanged', () => {
            this.loadVoices();
        });
        
        // Listen for language changes
        window.addEventListener('languageChanged', (event) => {
            this.currentLanguage = event.detail.language;
            this.stop(); // Stop current narration when language changes
        });
        
        // Add narration controls to the page
        this.addNarrationControls();
    }
    
    /**
     * Load available voices
     */
    loadVoices() {
        this.voices = this.synthesis.getVoices();
        console.log('Available voices:', this.voices.length);
    }
    
    /**
     * Get the best voice for the current language
     * @param {string} lang - Language code ('en' or 'ar')
     * @returns {SpeechSynthesisVoice|null} Best available voice
     */
    getBestVoice(lang = this.currentLanguage) {
        if (this.voices.length === 0) {
            this.loadVoices();
        }
        
        // Language-specific voice preferences
        const voicePreferences = {
            'en': ['en-US', 'en-GB', 'en-AU', 'en'],
            'ar': ['ar-SA', 'ar-EG', 'ar-AE', 'ar']
        };
        
        const preferences = voicePreferences[lang] || voicePreferences['en'];
        
        // Try to find the best matching voice
        for (const pref of preferences) {
            const voice = this.voices.find(v => 
                v.lang.toLowerCase().startsWith(pref.toLowerCase())
            );
            if (voice) {
                return voice;
            }
        }
        
        // Fallback to default voice
        return this.voices.find(v => v.default) || this.voices[0] || null;
    }
    
    /**
     * Speak the given text
     * @param {string} text - Text to speak
     * @param {Object} options - Speech options
     */
    speak(text, options = {}) {
        if (!this.synthesis) {
            console.warn('Speech synthesis not available');
            return;
        }
        
        // Stop current speech
        this.stop();
        
        // Clean text for speech
        const cleanText = this.cleanTextForSpeech(text);
        if (!cleanText.trim()) {
            return;
        }
        
        // Create utterance
        this.currentUtterance = new SpeechSynthesisUtterance(cleanText);
        
        // Set voice
        const voice = this.getBestVoice(options.lang || this.currentLanguage);
        if (voice) {
            this.currentUtterance.voice = voice;
        }
        
        // Set speech parameters
        this.currentUtterance.rate = options.rate || this.settings.rate;
        this.currentUtterance.pitch = options.pitch || this.settings.pitch;
        this.currentUtterance.volume = options.volume || this.settings.volume;
        
        // Set language
        this.currentUtterance.lang = options.lang || (this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US');
        
        // Event handlers
        this.currentUtterance.onstart = () => {
            this.isPlaying = true;
            this.updateNarrationControls();
            this.highlightCurrentText();
        };
        
        this.currentUtterance.onend = () => {
            this.isPlaying = false;
            this.updateNarrationControls();
            this.clearHighlight();
        };
        
        this.currentUtterance.onerror = (event) => {
            console.error('Speech synthesis error:', event);
            this.isPlaying = false;
            this.updateNarrationControls();
        };
        
        // Start speaking
        this.synthesis.speak(this.currentUtterance);
    }
    
    /**
     * Stop current speech
     */
    stop() {
        if (this.synthesis) {
            this.synthesis.cancel();
        }
        this.isPlaying = false;
        this.currentUtterance = null;
        this.updateNarrationControls();
        this.clearHighlight();
    }
    
    /**
     * Pause current speech
     */
    pause() {
        if (this.synthesis && this.isPlaying) {
            this.synthesis.pause();
            this.updateNarrationControls();
        }
    }
    
    /**
     * Resume paused speech
     */
    resume() {
        if (this.synthesis && this.synthesis.paused) {
            this.synthesis.resume();
            this.updateNarrationControls();
        }
    }
    
    /**
     * Clean text for speech synthesis
     * @param {string} text - Raw text
     * @returns {string} Cleaned text
     */
    cleanTextForSpeech(text) {
        // Remove HTML tags
        const div = document.createElement('div');
        div.innerHTML = text;
        let cleanText = div.textContent || div.innerText || '';
        
        // Remove extra whitespace
        cleanText = cleanText.replace(/\s+/g, ' ').trim();
        
        // Handle special characters for Arabic
        if (this.currentLanguage === 'ar') {
            // Add pauses for better Arabic pronunciation
            cleanText = cleanText.replace(/[.!?]/g, '$&\u00A0'); // Add non-breaking space after punctuation
        }
        
        return cleanText;
    }
    
    /**
     * Speak content from an element
     * @param {HTMLElement} element - Element containing text to speak
     */
    speakElement(element) {
        if (!element) return;
        
        const text = element.textContent || element.innerText;
        this.speak(text);
        
        // Store reference for highlighting
        this.currentElement = element;
    }
    
    /**
     * Speak accordion section content
     * @param {string} sectionId - ID of the accordion section
     */
    speakAccordionSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            const content = section.querySelector('.accordion-body');
            if (content) {
                this.speakElement(content);
            }
        }
    }
    
    /**
     * Add narration controls to the page
     */
    addNarrationControls() {
        // Check if controls already exist
        if (document.getElementById('narrationControls')) {
            return;
        }
        
        const controls = document.createElement('div');
        controls.id = 'narrationControls';
        controls.className = 'narration-controls';
        controls.innerHTML = `
            <div class="narration-panel">
                <div class="narration-buttons">
                    <button id="playBtn" class="btn btn-sm btn-primary" onclick="voiceNarration.playCurrentSection()" title="Play narration">
                        <i class="fas fa-play"></i>
                    </button>
                    <button id="pauseBtn" class="btn btn-sm btn-warning" onclick="voiceNarration.pause()" title="Pause narration" style="display: none;">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button id="stopBtn" class="btn btn-sm btn-danger" onclick="voiceNarration.stop()" title="Stop narration">
                        <i class="fas fa-stop"></i>
                    </button>
                    <button id="settingsBtn" class="btn btn-sm btn-outline-secondary" onclick="voiceNarration.toggleSettings()" title="Narration settings">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
                <div id="narrationSettings" class="narration-settings" style="display: none;">
                    <div class="setting-item">
                        <label>Speed:</label>
                        <input type="range" id="rateSlider" min="0.5" max="2" step="0.1" value="1" onchange="voiceNarration.updateRate(this.value)">
                        <span id="rateValue">1.0x</span>
                    </div>
                    <div class="setting-item">
                        <label>Pitch:</label>
                        <input type="range" id="pitchSlider" min="0.5" max="2" step="0.1" value="1" onchange="voiceNarration.updatePitch(this.value)">
                        <span id="pitchValue">1.0</span>
                    </div>
                    <div class="setting-item">
                        <label>Volume:</label>
                        <input type="range" id="volumeSlider" min="0" max="1" step="0.1" value="1" onchange="voiceNarration.updateVolume(this.value)">
                        <span id="volumeValue">100%</span>
                    </div>
                </div>
            </div>
        `;
        
        // Add CSS styles
        const style = document.createElement('style');
        style.textContent = `
            .narration-controls {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                padding: 1rem;
                min-width: 200px;
            }
            
            .narration-buttons {
                display: flex;
                gap: 0.5rem;
                margin-bottom: 0.5rem;
            }
            
            .narration-settings {
                border-top: 1px solid #eee;
                padding-top: 0.5rem;
            }
            
            .setting-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
            }
            
            .setting-item label {
                min-width: 50px;
                margin: 0;
            }
            
            .setting-item input[type="range"] {
                flex: 1;
            }
            
            .setting-item span {
                min-width: 40px;
                text-align: right;
                font-weight: 500;
            }
            
            .text-highlight {
                background-color: #fff3cd;
                border-radius: 4px;
                padding: 2px 4px;
            }
            
            html[dir="rtl"] .narration-controls {
                right: auto;
                left: 20px;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(controls);
    }
    
    /**
     * Update narration controls based on current state
     */
    updateNarrationControls() {
        const playBtn = document.getElementById('playBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        
        if (playBtn && pauseBtn && stopBtn) {
            if (this.isPlaying) {
                playBtn.style.display = 'none';
                pauseBtn.style.display = 'inline-block';
                stopBtn.disabled = false;
            } else {
                playBtn.style.display = 'inline-block';
                pauseBtn.style.display = 'none';
                stopBtn.disabled = this.synthesis && this.synthesis.speaking ? false : true;
            }
        }
    }
    
    /**
     * Play current section content
     */
    playCurrentSection() {
        // Try to find active accordion section
        const activeSection = document.querySelector('.accordion-collapse.show .accordion-body');
        if (activeSection) {
            this.speakElement(activeSection);
            return;
        }
        
        // Fallback to main content
        const mainContent = document.querySelector('.stage-content-section, .workspace-content, main');
        if (mainContent) {
            this.speakElement(mainContent);
        }
    }
    
    /**
     * Toggle settings panel
     */
    toggleSettings() {
        const settings = document.getElementById('narrationSettings');
        if (settings) {
            settings.style.display = settings.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    /**
     * Update speech rate
     */
    updateRate(value) {
        this.settings.rate = parseFloat(value);
        document.getElementById('rateValue').textContent = value + 'x';
    }
    
    /**
     * Update speech pitch
     */
    updatePitch(value) {
        this.settings.pitch = parseFloat(value);
        document.getElementById('pitchValue').textContent = value;
    }
    
    /**
     * Update speech volume
     */
    updateVolume(value) {
        this.settings.volume = parseFloat(value);
        document.getElementById('volumeValue').textContent = Math.round(value * 100) + '%';
    }
    
    /**
     * Highlight current text being spoken
     */
    highlightCurrentText() {
        if (this.currentElement) {
            this.currentElement.classList.add('text-highlight');
        }
    }
    
    /**
     * Clear text highlighting
     */
    clearHighlight() {
        const highlighted = document.querySelectorAll('.text-highlight');
        highlighted.forEach(el => el.classList.remove('text-highlight'));
        this.currentElement = null;
    }
    
    /**
     * Add narration button to accordion headers
     */
    addAccordionNarrationButtons() {
        const accordionHeaders = document.querySelectorAll('.accordion-button');
        accordionHeaders.forEach((header, index) => {
            if (!header.querySelector('.narration-btn')) {
                const btn = document.createElement('button');
                btn.className = 'btn btn-sm btn-outline-primary narration-btn ms-2';
                btn.innerHTML = '<i class="fas fa-volume-up"></i>';
                btn.title = this.currentLanguage === 'ar' ? 'استمع للمحتوى' : 'Listen to content';
                btn.onclick = (e) => {
                    e.stopPropagation();
                    const targetId = header.getAttribute('data-bs-target').substring(1);
                    this.speakAccordionSection(targetId);
                };
                header.appendChild(btn);
            }
        });
    }
    
    /**
     * Check if speech synthesis is supported
     * @returns {boolean} True if supported
     */
    isSupported() {
        return !!this.synthesis;
    }
    
    /**
     * Get available voices for a language
     * @param {string} lang - Language code
     * @returns {Array} Available voices
     */
    getVoicesForLanguage(lang) {
        return this.voices.filter(voice => 
            voice.lang.toLowerCase().startsWith(lang.toLowerCase())
        );
    }
}

// Initialize voice narration system
let voiceNarration;

document.addEventListener('DOMContentLoaded', function() {
    voiceNarration = new VoiceNarration();
    
    // Add narration buttons to existing accordions
    setTimeout(() => {
        voiceNarration.addAccordionNarrationButtons();
    }, 1000);
    
    // Listen for dynamic content changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                // Check if new accordion content was added
                const addedNodes = Array.from(mutation.addedNodes);
                const hasAccordion = addedNodes.some(node => 
                    node.nodeType === 1 && 
                    (node.classList?.contains('accordion') || node.querySelector?.('.accordion'))
                );
                
                if (hasAccordion) {
                    setTimeout(() => {
                        voiceNarration.addAccordionNarrationButtons();
                    }, 100);
                }
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// Export for global use
window.VoiceNarration = VoiceNarration;
