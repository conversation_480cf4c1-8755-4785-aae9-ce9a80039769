/**
 * Medical Device Innovation Guide - Enhanced Navigation System
 * Unified navigation and integration across all pages
 */

class EnhancedNavigation {
    constructor() {
        this.currentPage = this.detectCurrentPage();
        this.navigationData = {
            pages: [
                {
                    id: 'home',
                    title: { en: 'Home', ar: 'الرئيسية' },
                    url: 'index.html',
                    icon: 'fas fa-home',
                    description: { en: 'Main landing page', ar: 'الصفحة الرئيسية' }
                },
                {
                    id: 'interactive-book',
                    title: { en: 'Interactive Academic Book', ar: 'الكتاب الأكاديمي التفاعلي' },
                    url: 'Interactive Academic Book Medical Device Innovation Guide.html',
                    icon: 'fas fa-book-open',
                    description: { en: 'Comprehensive interactive guide', ar: 'الدليل التفاعلي الشامل' }
                },
                {
                    id: 'dashboard',
                    title: { en: 'Learning Dashboard', ar: 'لوحة التعلم' },
                    url: 'dashboard.html',
                    icon: 'fas fa-tachometer-alt',
                    description: { en: 'Track your progress', ar: 'تتبع تقدمك' }
                },
                {
                    id: 'stage-01',
                    title: { en: 'Stage 1: Introduction', ar: 'المرحلة 1: مقدمة' },
                    url: 'stage-01-bilingual.html',
                    icon: 'fas fa-play-circle',
                    description: { en: 'Start your journey', ar: 'ابدأ رحلتك' }
                },
                {
                    id: 'stage-02',
                    title: { en: 'Stage 2: Market Landscape', ar: 'المرحلة 2: المشهد السوقي' },
                    url: 'stage-02-bilingual.html',
                    icon: 'fas fa-chart-line',
                    description: { en: 'Market analysis', ar: 'تحليل السوق' }
                },
                {
                    id: 'advanced-demo',
                    title: { en: 'Advanced Demo', ar: 'العرض المتقدم' },
                    url: 'advanced-demo.html',
                    icon: 'fas fa-rocket',
                    description: { en: 'Feature showcase', ar: 'عرض الميزات' }
                },
                {
                    id: 'content-manager',
                    title: { en: 'Content Management', ar: 'إدارة المحتوى' },
                    url: 'content-manager.html',
                    icon: 'fas fa-cogs',
                    description: { en: 'Manage content', ar: 'إدارة المحتوى' }
                },
                {
                    id: 'test-bilingual',
                    title: { en: 'Test Features', ar: 'اختبار الميزات' },
                    url: 'test-bilingual.html',
                    icon: 'fas fa-flask',
                    description: { en: 'Test functionality', ar: 'اختبار الوظائف' }
                }
            ],
            stages: [
                { id: 'stage-03', title: { en: 'Innovation Process', ar: 'عملية الابتكار' }, url: 'stage-03-bilingual.html' },
                { id: 'stage-04', title: { en: 'Understanding Opportunity', ar: 'فهم الفرصة' }, url: 'stage-04-bilingual.html' },
                { id: 'stage-05', title: { en: 'Needs Discovery', ar: 'اكتشاف الاحتياجات' }, url: 'stage-05-bilingual.html' },
                { id: 'stage-06', title: { en: 'Idea Generation', ar: 'توليد الأفكار' }, url: 'stage-06-bilingual.html' },
                { id: 'stage-07', title: { en: 'Testing Your Idea', ar: 'اختبار فكرتك' }, url: 'stage-07-bilingual.html' },
                { id: 'stage-08', title: { en: 'Clinical Trials', ar: 'التجارب السريرية' }, url: 'stage-08-bilingual.html' },
                { id: 'stage-09', title: { en: 'Reliability Considerations', ar: 'اعتبارات الموثوقية' }, url: 'stage-09-bilingual.html' },
                { id: 'stage-10', title: { en: 'Innovation Notebooks', ar: 'دفاتر الابتكار' }, url: 'stage-10-bilingual.html' }
            ]
        };
        
        this.init();
    }
    
    /**
     * Initialize the enhanced navigation system
     */
    init() {
        this.addNavigationBar();
        this.addBreadcrumbs();
        this.addQuickActions();
        this.setupKeyboardShortcuts();
        this.trackPageVisit();
    }
    
    /**
     * Detect current page from URL
     */
    detectCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';
        return filename.replace('.html', '');
    }
    
    /**
     * Add enhanced navigation bar
     */
    addNavigationBar() {
        // Check if navigation already exists
        if (document.getElementById('enhanced-nav')) return;
        
        const nav = document.createElement('div');
        nav.id = 'enhanced-nav';
        nav.className = 'enhanced-navigation';
        nav.innerHTML = this.generateNavigationHTML();
        
        // Insert at the top of the body
        document.body.insertBefore(nav, document.body.firstChild);
        
        // Add event listeners
        this.setupNavigationEvents();
    }
    
    /**
     * Generate navigation HTML
     */
    generateNavigationHTML() {
        const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
        
        return `
            <nav class="enhanced-nav-bar">
                <div class="nav-container">
                    <div class="nav-brand">
                        <a href="index.html" class="brand-link">
                            <i class="fas fa-heartbeat me-2"></i>
                            <span data-i18n-key="site_title">MedInnovate Hub</span>
                        </a>
                    </div>
                    
                    <div class="nav-menu" id="navMenu">
                        <div class="nav-section">
                            <h6 data-i18n-key="main_pages">Main Pages</h6>
                            ${this.generatePageLinks(currentLang)}
                        </div>
                        
                        <div class="nav-section">
                            <h6 data-i18n-key="learning_stages">Learning Stages</h6>
                            ${this.generateStageLinks(currentLang)}
                        </div>
                    </div>
                    
                    <div class="nav-actions">
                        <button class="nav-toggle" id="navToggle">
                            <i class="fas fa-bars"></i>
                        </button>
                        
                        <div class="quick-actions">
                            <button class="action-btn" onclick="enhancedNav.toggleSearch()" title="Search">
                                <i class="fas fa-search"></i>
                            </button>
                            
                            <button class="action-btn" onclick="enhancedNav.toggleBookmarks()" title="Bookmarks">
                                <i class="fas fa-bookmark"></i>
                            </button>
                            
                            <button class="action-btn" onclick="enhancedNav.showProgress()" title="Progress">
                                <i class="fas fa-chart-pie"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Search Panel -->
                <div class="search-panel" id="searchPanel" style="display: none;">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search pages, stages, or content..." id="searchInput">
                        <div class="search-results" id="searchResults"></div>
                    </div>
                </div>
                
                <!-- Bookmarks Panel -->
                <div class="bookmarks-panel" id="bookmarksPanel" style="display: none;">
                    <div class="bookmarks-container">
                        <h6 data-i18n-key="bookmarks">Bookmarks</h6>
                        <div class="bookmarks-list" id="bookmarksList">
                            <p class="text-muted" data-i18n-key="no_bookmarks">No bookmarks yet</p>
                        </div>
                    </div>
                </div>
            </nav>
        `;
    }
    
    /**
     * Generate page links
     */
    generatePageLinks(lang) {
        return this.navigationData.pages.map(page => {
            const isActive = this.currentPage === page.id || 
                           (this.currentPage === 'index' && page.id === 'home');
            
            return `
                <a href="${page.url}" class="nav-link ${isActive ? 'active' : ''}" data-page="${page.id}">
                    <i class="${page.icon} me-2"></i>
                    <span>${page.title[lang]}</span>
                </a>
            `;
        }).join('');
    }
    
    /**
     * Generate stage links
     */
    generateStageLinks(lang) {
        return this.navigationData.stages.map(stage => {
            const isActive = this.currentPage === stage.id;
            
            return `
                <a href="${stage.url}" class="nav-link ${isActive ? 'active' : ''}" data-stage="${stage.id}">
                    <span class="stage-number">${stage.id.split('-')[1]}</span>
                    <span>${stage.title[lang]}</span>
                </a>
            `;
        }).join('');
    }
    
    /**
     * Setup navigation events
     */
    setupNavigationEvents() {
        // Mobile menu toggle
        const navToggle = document.getElementById('navToggle');
        const navMenu = document.getElementById('navMenu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
            });
        }
        
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.performSearch(e.target.value);
            });
        }
        
        // Close panels when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-panel') && !e.target.closest('[onclick*="toggleSearch"]')) {
                this.hideSearch();
            }
            if (!e.target.closest('.bookmarks-panel') && !e.target.closest('[onclick*="toggleBookmarks"]')) {
                this.hideBookmarks();
            }
        });
    }
    
    /**
     * Add breadcrumbs
     */
    addBreadcrumbs() {
        const breadcrumbContainer = document.createElement('div');
        breadcrumbContainer.className = 'breadcrumb-container';
        breadcrumbContainer.innerHTML = this.generateBreadcrumbs();
        
        // Insert after navigation
        const nav = document.getElementById('enhanced-nav');
        if (nav && nav.nextSibling) {
            nav.parentNode.insertBefore(breadcrumbContainer, nav.nextSibling);
        }
    }
    
    /**
     * Generate breadcrumbs
     */
    generateBreadcrumbs() {
        const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
        const currentPageData = this.findPageData(this.currentPage);
        
        if (!currentPageData) return '';
        
        return `
            <nav class="breadcrumb-nav">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="index.html">
                            <i class="fas fa-home me-1"></i>
                            <span data-i18n-key="home">Home</span>
                        </a>
                    </li>
                    <li class="breadcrumb-item active">
                        <i class="${currentPageData.icon} me-1"></i>
                        <span>${currentPageData.title[currentLang]}</span>
                    </li>
                </ol>
            </nav>
        `;
    }
    
    /**
     * Find page data by ID
     */
    findPageData(pageId) {
        return [...this.navigationData.pages, ...this.navigationData.stages]
            .find(item => item.id === pageId);
    }
    
    /**
     * Add quick actions floating button
     */
    addQuickActions() {
        const quickActions = document.createElement('div');
        quickActions.className = 'floating-actions';
        quickActions.innerHTML = `
            <div class="fab-container">
                <button class="fab-main" onclick="enhancedNav.toggleFAB()">
                    <i class="fas fa-plus"></i>
                </button>
                <div class="fab-menu" id="fabMenu">
                    <button class="fab-item" onclick="enhancedNav.goToNextStage()" title="Next Stage">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <button class="fab-item" onclick="enhancedNav.goToPrevStage()" title="Previous Stage">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button class="fab-item" onclick="enhancedNav.bookmarkPage()" title="Bookmark">
                        <i class="fas fa-bookmark"></i>
                    </button>
                    <button class="fab-item" onclick="enhancedNav.shareProgress()" title="Share">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(quickActions);
    }
    
    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.toggleSearch();
            }
            
            // Ctrl/Cmd + B for bookmarks
            if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                e.preventDefault();
                this.toggleBookmarks();
            }
            
            // Arrow keys for navigation
            if (e.altKey) {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    this.goToPrevStage();
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    this.goToNextStage();
                }
            }
        });
    }
    
    /**
     * Track page visit for analytics
     */
    trackPageVisit() {
        if (window.analyticsPerformance) {
            window.analyticsPerformance.trackInteraction('page_visit', {
                page: this.currentPage,
                timestamp: Date.now(),
                referrer: document.referrer
            });
        }
    }
    
    // Public methods for interaction
    toggleSearch() {
        const panel = document.getElementById('searchPanel');
        const input = document.getElementById('searchInput');
        
        if (panel.style.display === 'none') {
            panel.style.display = 'block';
            input.focus();
        } else {
            panel.style.display = 'none';
        }
    }
    
    hideSearch() {
        const panel = document.getElementById('searchPanel');
        if (panel) panel.style.display = 'none';
    }
    
    toggleBookmarks() {
        const panel = document.getElementById('bookmarksPanel');
        
        if (panel.style.display === 'none') {
            panel.style.display = 'block';
            this.loadBookmarks();
        } else {
            panel.style.display = 'none';
        }
    }
    
    hideBookmarks() {
        const panel = document.getElementById('bookmarksPanel');
        if (panel) panel.style.display = 'none';
    }
    
    performSearch(query) {
        if (!query.trim()) {
            document.getElementById('searchResults').innerHTML = '';
            return;
        }
        
        const results = this.searchContent(query);
        this.displaySearchResults(results);
    }
    
    searchContent(query) {
        const allPages = [...this.navigationData.pages, ...this.navigationData.stages];
        const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
        
        return allPages.filter(page => {
            const title = page.title[currentLang].toLowerCase();
            const description = page.description ? page.description[currentLang].toLowerCase() : '';
            const searchTerm = query.toLowerCase();
            
            return title.includes(searchTerm) || description.includes(searchTerm);
        });
    }
    
    displaySearchResults(results) {
        const container = document.getElementById('searchResults');
        const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
        
        if (results.length === 0) {
            container.innerHTML = '<p class="no-results">No results found</p>';
            return;
        }
        
        container.innerHTML = results.map(result => `
            <a href="${result.url}" class="search-result-item">
                <i class="${result.icon} me-2"></i>
                <div>
                    <div class="result-title">${result.title[currentLang]}</div>
                    ${result.description ? `<div class="result-desc">${result.description[currentLang]}</div>` : ''}
                </div>
            </a>
        `).join('');
    }
    
    toggleFAB() {
        const menu = document.getElementById('fabMenu');
        menu.classList.toggle('active');
    }
    
    goToNextStage() {
        // Implementation for next stage navigation
        console.log('Navigate to next stage');
    }
    
    goToPrevStage() {
        // Implementation for previous stage navigation
        console.log('Navigate to previous stage');
    }
    
    bookmarkPage() {
        // Implementation for bookmarking
        console.log('Bookmark current page');
    }
    
    shareProgress() {
        // Implementation for sharing progress
        console.log('Share progress');
    }
    
    loadBookmarks() {
        // Implementation for loading bookmarks
        console.log('Load bookmarks');
    }
    
    showProgress() {
        // Implementation for showing progress
        console.log('Show progress');
    }
}

// Initialize enhanced navigation when DOM is ready
let enhancedNav;

document.addEventListener('DOMContentLoaded', function() {
    enhancedNav = new EnhancedNavigation();
    
    // Make it globally available
    window.enhancedNav = enhancedNav;
});

// Export for module use
window.EnhancedNavigation = EnhancedNavigation;
