import React from 'react';
import { bookData } from '../data/bookContent';
import { ChevronRight, BookOpen, FileText, Users, Lightbulb, Play, X } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface SidebarProps {
  currentChapter: string;
  onChapterSelect: (chapterId: string) => void;
  onLectureSelect: (lectureId: string) => void;
  isOpen: boolean;
  onClose: () => void;
  activeTab: 'chapters' | 'lectures';
  onTabChange: (tab: 'chapters' | 'lectures') => void;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  currentChapter, 
  onChapterSelect, 
  onLectureSelect,
  isOpen, 
  onClose,
  activeTab,
  onTabChange
}) => {
  const { language, t } = useLanguage();

  const getChapterIcon = (index: number) => {
    const icons = [BookOpen, Lightbulb, FileText, Users, FileText, Users, Lightbulb, BookOpen];
    const IconComponent = icons[index % icons.length];
    return <IconComponent className="h-5 w-5" />;
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}
      
      <aside className={`
        fixed lg:sticky top-0 h-screen bg-white border-r border-gray-200 z-50 lg:z-0
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : (language === 'ar' ? 'translate-x-full lg:translate-x-0' : '-translate-x-full lg:translate-x-0')}
        w-80 lg:w-96
      `}>
        <div className="h-full flex flex-col">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                {activeTab === 'chapters' ? t('table_of_contents') : t('interactive_lectures')}
              </h2>
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-gray-100 lg:hidden"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>

            {/* Tab Navigation */}
            <div className="flex bg-gray-100 rounded-lg p-1 mb-4">
              <button
                onClick={() => onTabChange('chapters')}
                className={`flex-1 flex items-center justify-center space-x-2 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-all ${
                  activeTab === 'chapters'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <BookOpen className="h-4 w-4" />
                <span>{t('table_of_contents')}</span>
              </button>
              <button
                onClick={() => onTabChange('lectures')}
                className={`flex-1 flex items-center justify-center space-x-2 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-all ${
                  activeTab === 'lectures'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Play className="h-4 w-4" />
                <span>{t('interactive_lectures')}</span>
              </button>
            </div>
            
            {/* Book Info */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">{t('book_info')}</h3>
              <p className="text-sm text-blue-700 mb-1">
                {t('author')}: {language === 'ar' ? bookData.metadata.arabicAuthor : bookData.metadata.author}
              </p>
              <p className="text-sm text-blue-700 mb-1">{t('year')}: {bookData.metadata.year}</p>
              <p className="text-sm text-blue-700 mb-1">
                {t('publisher')}: {language === 'ar' ? bookData.metadata.arabicPublisher : bookData.metadata.publisher}
              </p>
              <div className="mt-2 pt-2 border-t border-blue-200">
                <p className="text-xs text-blue-600 mb-1">{t('contact')}: {bookData.metadata.email}</p>
                <p className="text-xs text-blue-600">
                  {bookData.metadata.phones.join(' | ')}
                </p>
              </div>
            </div>
          </div>

          <nav className="flex-1 overflow-y-auto p-4">
            {activeTab === 'chapters' ? (
              <ul className="space-y-2">
                {bookData.chapters.map((chapter, index) => (
                  <li key={chapter.id}>
                    <button
                      onClick={() => {
                        onChapterSelect(chapter.id);
                        onClose();
                      }}
                      className={`
                        w-full text-right p-4 rounded-lg transition-all duration-200
                        flex items-center space-x-3 space-x-reverse
                        hover:bg-gray-50 hover:shadow-sm
                        ${currentChapter === chapter.id 
                          ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-500' 
                          : 'text-gray-700 hover:text-gray-900'
                        }
                      `}
                    >
                      <div className={`
                        p-2 rounded-lg flex-shrink-0
                        ${currentChapter === chapter.id 
                          ? 'bg-blue-100 text-blue-600' 
                          : 'bg-gray-100 text-gray-600'
                        }
                      `}>
                        {getChapterIcon(index)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium truncate">
                            {t('chapter')} {index + 1}
                          </span>
                          <ChevronRight className={`
                            h-4 w-4 transform transition-transform
                            ${currentChapter === chapter.id ? 'rotate-90' : ''}
                          `} />
                        </div>
                        <p className="text-sm text-gray-600 truncate mt-1">
                          {language === 'ar' ? chapter.arabicTitle : chapter.title}
                        </p>
                      </div>
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <ul className="space-y-2">
                {bookData.lectures.map((lecture, index) => (
                  <li key={lecture.id}>
                    <button
                      onClick={() => {
                        onLectureSelect(lecture.id);
                        onClose();
                      }}
                      className="w-full text-right p-4 rounded-lg transition-all duration-200 flex items-center space-x-3 space-x-reverse hover:bg-gray-50 hover:shadow-sm text-gray-700 hover:text-gray-900"
                    >
                      <div className="p-2 rounded-lg flex-shrink-0 bg-green-100 text-green-600">
                        <Play className="h-5 w-5" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium truncate">
                            {t('lecture')} {index + 1}
                          </span>
                          <span className="text-xs text-gray-500">
                            {lecture.duration} {t('minutes')}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 truncate mt-1">
                          {language === 'ar' ? lecture.arabicTitle : lecture.title}
                        </p>
                      </div>
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </nav>

          <div className="p-4 border-t border-gray-200">
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-xs text-gray-600 text-center">
                {t('academic_book')}
              </p>
              <p className="text-xs text-gray-500 text-center mt-1">
                © 2025 {t('copyright')} - Dr. Mohammed Yagoub Esmail
              </p>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;