import React from 'react';
import { ChevronUp, Download, Share2, BookmarkPlus, ArrowLeft, ArrowRight } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface FloatingActionsProps {
  currentChapterIndex: number;
  totalChapters: number;
  onNavigate: (direction: 'prev' | 'next') => void;
}

const FloatingActions: React.FC<FloatingActionsProps> = ({ 
  currentChapterIndex, 
  totalChapters, 
  onNavigate 
}) => {
  const { language, t } = useLanguage();

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className={`fixed bottom-6 ${language === 'ar' ? 'left-6' : 'right-6'} z-30`}>
      <div className="flex flex-col space-y-3">
        {/* Navigation buttons */}
        <div className={`flex ${language === 'ar' ? 'space-x-2' : 'space-x-2 space-x-reverse'}`}>
          {currentChapterIndex > 0 && (
            <button
              onClick={() => onNavigate('prev')}
              className="bg-white shadow-lg hover:shadow-xl border border-gray-200 rounded-full p-3 hover:bg-gray-50 transition-all duration-200 group"
              title={t('previous_chapter')}
            >
              {language === 'ar' ? (
                <ArrowLeft className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
              ) : (
                <ArrowRight className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
              )}
            </button>
          )}
          
          {currentChapterIndex < totalChapters - 1 && (
            <button
              onClick={() => onNavigate('next')}
              className="bg-white shadow-lg hover:shadow-xl border border-gray-200 rounded-full p-3 hover:bg-gray-50 transition-all duration-200 group"
              title={t('next_chapter')}
            >
              {language === 'ar' ? (
                <ArrowRight className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
              ) : (
                <ArrowLeft className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
              )}
            </button>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex flex-col space-y-2">
          <button
            onClick={scrollToTop}
            className="bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl rounded-full p-3 text-white transition-all duration-200"
            title={t('back_to_top')}
          >
            <ChevronUp className="h-5 w-5" />
          </button>
          
          <button
            className="bg-white shadow-lg hover:shadow-xl border border-gray-200 rounded-full p-3 hover:bg-gray-50 transition-all duration-200 group"
            title={t('bookmark')}
          >
            <BookmarkPlus className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
          </button>
          
          <button
            className="bg-white shadow-lg hover:shadow-xl border border-gray-200 rounded-full p-3 hover:bg-gray-50 transition-all duration-200 group"
            title={t('share')}
          >
            <Share2 className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
          </button>
          
          <button
            className="bg-white shadow-lg hover:shadow-xl border border-gray-200 rounded-full p-3 hover:bg-gray-50 transition-all duration-200 group"
            title={t('download_chapter')}
          >
            <Download className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FloatingActions;