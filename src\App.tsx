import React, { useState, useEffect } from 'react';
import { LanguageProvider, useLanguage } from './contexts/LanguageContext';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import ChapterContent from './components/ChapterContent';
import LectureContent from './components/LectureContent';
import FloatingActions from './components/FloatingActions';
import ProgressBar from './components/ProgressBar';
import { bookData } from './data/bookContent';

function AppContent() {
  const [currentChapter, setCurrentChapter] = useState('chapter-1');
  const [currentLecture, setCurrentLecture] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'chapters' | 'lectures'>('chapters');
  const { language, t } = useLanguage();

  const currentChapterData = bookData.chapters.find(chapter => chapter.id === currentChapter);
  const currentLectureData = currentLecture ? bookData.lectures.find(lecture => lecture.id === currentLecture) : null;
  const currentChapterIndex = bookData.chapters.findIndex(chapter => chapter.id === currentChapter);

  const handleChapterSelect = (chapterId: string) => {
    setCurrentChapter(chapterId);
    setCurrentLecture(null);
    setActiveTab('chapters');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleLectureSelect = (lectureId: string) => {
    setCurrentLecture(lectureId);
    setCurrentChapter('');
    setActiveTab('lectures');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleNavigation = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && currentChapterIndex > 0) {
      setCurrentChapter(bookData.chapters[currentChapterIndex - 1].id);
      setCurrentLecture(null);
    } else if (direction === 'next' && currentChapterIndex < bookData.chapters.length - 1) {
      setCurrentChapter(bookData.chapters[currentChapterIndex + 1].id);
      setCurrentLecture(null);
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Set document direction based on language
  useEffect(() => {
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
  }, [language]);

  if (!currentChapterData && !currentLectureData) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">{t('chapter_not_found')}</h2>
          <p className="text-gray-600">{t('chapter_not_available')}</p>
        </div>
      </div>
    );
  }

  const currentTitle = currentLectureData 
    ? (language === 'ar' ? currentLectureData.arabicTitle : currentLectureData.title)
    : (currentChapterData ? (language === 'ar' ? currentChapterData.arabicTitle : currentChapterData.title) : '');

  return (
    <div className="min-h-screen bg-gray-50" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <ProgressBar currentChapter={currentChapterIndex} totalChapters={bookData.chapters.length} />
      
      <Header 
        onMenuClick={() => setSidebarOpen(true)}
        currentChapter={currentTitle}
      />
      
      <div className="flex">
        <Sidebar 
          currentChapter={currentChapter}
          onChapterSelect={handleChapterSelect}
          onLectureSelect={handleLectureSelect}
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
        
        <main className={`flex-1 ${language === 'ar' ? 'lg:ml-96' : 'lg:mr-96'}`}>
          <div className="py-8 px-4 sm:px-6 lg:px-8">
            {currentLectureData ? (
              <LectureContent lecture={currentLectureData} />
            ) : currentChapterData ? (
              <ChapterContent chapter={currentChapterData} />
            ) : null}
          </div>
        </main>
      </div>

      {!currentLectureData && (
        <FloatingActions 
          currentChapterIndex={currentChapterIndex}
          totalChapters={bookData.chapters.length}
          onNavigate={handleNavigation}
        />
      )}
    </div>
  );
}

function App() {
  return (
    <LanguageProvider>
      <AppContent />
    </LanguageProvider>
  );
}

export default App;