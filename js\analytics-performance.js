/**
 * Medical Device Innovation Guide - Analytics and Performance Optimization
 * Language usage analytics, performance monitoring, and optimization features
 */

class AnalyticsPerformance {
    constructor() {
        this.analytics = {
            languageUsage: {},
            stageVisits: {},
            timeSpent: {},
            interactions: [],
            performance: {},
            userPreferences: {}
        };
        
        this.performanceObserver = null;
        this.intersectionObserver = null;
        this.startTime = Date.now();
        
        this.init();
    }
    
    /**
     * Initialize analytics and performance monitoring
     */
    init() {
        this.loadStoredAnalytics();
        this.setupPerformanceMonitoring();
        this.setupLanguageTracking();
        this.setupContentLazyLoading();
        this.setupUserInteractionTracking();
        this.startSessionTracking();
        
        // Save analytics periodically
        setInterval(() => {
            this.saveAnalytics();
        }, 30000); // Save every 30 seconds
        
        // Save on page unload
        window.addEventListener('beforeunload', () => {
            this.endSession();
            this.saveAnalytics();
        });
    }
    
    /**
     * Load stored analytics data
     */
    loadStoredAnalytics() {
        try {
            const stored = localStorage.getItem('medDevice_analytics');
            if (stored) {
                this.analytics = { ...this.analytics, ...JSON.parse(stored) };
            }
        } catch (error) {
            console.warn('Failed to load analytics data:', error);
        }
    }
    
    /**
     * Save analytics data to localStorage
     */
    saveAnalytics() {
        try {
            localStorage.setItem('medDevice_analytics', JSON.stringify(this.analytics));
        } catch (error) {
            console.warn('Failed to save analytics data:', error);
        }
    }
    
    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            this.recordPageLoadMetrics();
        });
        
        // Monitor resource loading
        if ('PerformanceObserver' in window) {
            this.performanceObserver = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    this.recordPerformanceEntry(entry);
                });
            });
            
            this.performanceObserver.observe({ entryTypes: ['navigation', 'resource', 'measure'] });
        }
        
        // Monitor memory usage (if available)
        if ('memory' in performance) {
            setInterval(() => {
                this.recordMemoryUsage();
            }, 60000); // Every minute
        }
    }
    
    /**
     * Record page load metrics
     */
    recordPageLoadMetrics() {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.analytics.performance.pageLoad = {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                totalTime: navigation.loadEventEnd - navigation.fetchStart,
                timestamp: Date.now()
            };
        }
    }
    
    /**
     * Record performance entry
     */
    recordPerformanceEntry(entry) {
        if (entry.entryType === 'resource') {
            // Track resource loading times
            if (!this.analytics.performance.resources) {
                this.analytics.performance.resources = [];
            }
            
            this.analytics.performance.resources.push({
                name: entry.name,
                duration: entry.duration,
                size: entry.transferSize || 0,
                type: this.getResourceType(entry.name),
                timestamp: Date.now()
            });
            
            // Keep only last 100 resource entries
            if (this.analytics.performance.resources.length > 100) {
                this.analytics.performance.resources = this.analytics.performance.resources.slice(-100);
            }
        }
    }
    
    /**
     * Get resource type from URL
     */
    getResourceType(url) {
        if (url.includes('.js')) return 'javascript';
        if (url.includes('.css')) return 'stylesheet';
        if (url.includes('.png') || url.includes('.jpg') || url.includes('.svg')) return 'image';
        if (url.includes('.woff') || url.includes('.ttf')) return 'font';
        return 'other';
    }
    
    /**
     * Record memory usage
     */
    recordMemoryUsage() {
        if ('memory' in performance) {
            this.analytics.performance.memory = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };
        }
    }
    
    /**
     * Setup language usage tracking
     */
    setupLanguageTracking() {
        // Track initial language
        const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
        this.trackLanguageUsage(currentLang);
        
        // Track language changes
        window.addEventListener('languageChanged', (event) => {
            this.trackLanguageUsage(event.detail.language);
            this.trackInteraction('language_change', {
                from: event.detail.previousLanguage || 'unknown',
                to: event.detail.language
            });
        });
    }
    
    /**
     * Track language usage
     */
    trackLanguageUsage(language) {
        if (!this.analytics.languageUsage[language]) {
            this.analytics.languageUsage[language] = {
                sessions: 0,
                totalTime: 0,
                lastUsed: null
            };
        }
        
        this.analytics.languageUsage[language].sessions++;
        this.analytics.languageUsage[language].lastUsed = Date.now();
        this.currentLanguageStartTime = Date.now();
    }
    
    /**
     * Setup content lazy loading
     */
    setupContentLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        this.loadContentOnDemand(entry.target);
                    }
                });
            }, {
                rootMargin: '100px' // Load content 100px before it comes into view
            });
            
            // Observe lazy-loadable elements
            this.observeLazyElements();
        }
    }
    
    /**
     * Observe elements for lazy loading
     */
    observeLazyElements() {
        const lazyElements = document.querySelectorAll('[data-lazy-load]');
        lazyElements.forEach((element) => {
            this.intersectionObserver.observe(element);
        });
    }
    
    /**
     * Load content on demand
     */
    loadContentOnDemand(element) {
        const contentType = element.getAttribute('data-lazy-load');
        const stageKey = element.getAttribute('data-stage-key');
        
        if (contentType === 'stage-content' && stageKey) {
            // Load stage content dynamically
            this.loadStageContentLazy(stageKey, element);
        }
        
        // Stop observing this element
        this.intersectionObserver.unobserve(element);
    }
    
    /**
     * Load stage content lazily
     */
    loadStageContentLazy(stageKey, element) {
        const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
        
        if (window.UIBuilder && window.UIBuilder.renderGuideContent) {
            const containerId = element.id || 'guideAccordion';
            window.UIBuilder.renderGuideContent(stageKey, currentLang, containerId);
            
            this.trackInteraction('lazy_content_load', {
                stageKey: stageKey,
                language: currentLang
            });
        }
    }
    
    /**
     * Setup user interaction tracking
     */
    setupUserInteractionTracking() {
        // Track clicks
        document.addEventListener('click', (event) => {
            this.trackClick(event);
        });
        
        // Track stage visits
        this.trackStageVisit();
        
        // Track scroll depth
        this.setupScrollTracking();
        
        // Track form interactions
        this.setupFormTracking();
    }
    
    /**
     * Track click events
     */
    trackClick(event) {
        const target = event.target.closest('[data-track-click]');
        if (target) {
            const trackingData = {
                element: target.tagName.toLowerCase(),
                action: target.getAttribute('data-track-click'),
                text: target.textContent?.substring(0, 50) || '',
                timestamp: Date.now()
            };
            
            this.trackInteraction('click', trackingData);
        }
    }
    
    /**
     * Track stage visits
     */
    trackStageVisit() {
        const stageKey = document.body.getAttribute('data-stage-key');
        if (stageKey) {
            if (!this.analytics.stageVisits[stageKey]) {
                this.analytics.stageVisits[stageKey] = {
                    visits: 0,
                    totalTime: 0,
                    lastVisit: null
                };
            }
            
            this.analytics.stageVisits[stageKey].visits++;
            this.analytics.stageVisits[stageKey].lastVisit = Date.now();
            this.currentStageStartTime = Date.now();
        }
    }
    
    /**
     * Setup scroll tracking
     */
    setupScrollTracking() {
        let maxScroll = 0;
        let scrollTimeout;
        
        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round(
                (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
            );
            
            if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
            }
            
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.trackInteraction('scroll', {
                    maxDepth: maxScroll,
                    currentDepth: scrollPercent
                });
            }, 1000);
        });
    }
    
    /**
     * Setup form tracking
     */
    setupFormTracking() {
        document.addEventListener('submit', (event) => {
            const form = event.target;
            if (form.tagName === 'FORM') {
                this.trackInteraction('form_submit', {
                    formId: form.id || 'unknown',
                    fields: form.elements.length
                });
            }
        });
    }
    
    /**
     * Track user interaction
     */
    trackInteraction(type, data = {}) {
        this.analytics.interactions.push({
            type: type,
            data: data,
            timestamp: Date.now(),
            language: window.I18n ? window.I18n.getCurrentLanguage() : 'en',
            page: window.location.pathname
        });
        
        // Keep only last 1000 interactions
        if (this.analytics.interactions.length > 1000) {
            this.analytics.interactions = this.analytics.interactions.slice(-1000);
        }
    }
    
    /**
     * Start session tracking
     */
    startSessionTracking() {
        this.sessionStartTime = Date.now();
        this.trackInteraction('session_start', {
            userAgent: navigator.userAgent,
            language: navigator.language,
            screenResolution: `${screen.width}x${screen.height}`,
            viewport: `${window.innerWidth}x${window.innerHeight}`
        });
    }
    
    /**
     * End session tracking
     */
    endSession() {
        const sessionDuration = Date.now() - this.sessionStartTime;
        
        // Update language usage time
        if (this.currentLanguageStartTime) {
            const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
            const languageTime = Date.now() - this.currentLanguageStartTime;
            this.analytics.languageUsage[currentLang].totalTime += languageTime;
        }
        
        // Update stage visit time
        if (this.currentStageStartTime) {
            const stageKey = document.body.getAttribute('data-stage-key');
            if (stageKey) {
                const stageTime = Date.now() - this.currentStageStartTime;
                this.analytics.stageVisits[stageKey].totalTime += stageTime;
            }
        }
        
        this.trackInteraction('session_end', {
            duration: sessionDuration
        });
    }
    
    /**
     * Get analytics summary
     */
    getAnalyticsSummary() {
        return {
            languageUsage: this.analytics.languageUsage,
            stageVisits: this.analytics.stageVisits,
            totalInteractions: this.analytics.interactions.length,
            performance: this.analytics.performance,
            sessionCount: this.analytics.interactions.filter(i => i.type === 'session_start').length
        };
    }
    
    /**
     * Get language preference insights
     */
    getLanguageInsights() {
        const usage = this.analytics.languageUsage;
        const total = Object.values(usage).reduce((sum, lang) => sum + lang.sessions, 0);
        
        return Object.entries(usage).map(([lang, data]) => ({
            language: lang,
            sessions: data.sessions,
            percentage: total > 0 ? Math.round((data.sessions / total) * 100) : 0,
            averageTime: data.sessions > 0 ? Math.round(data.totalTime / data.sessions / 1000) : 0,
            lastUsed: data.lastUsed ? new Date(data.lastUsed).toLocaleDateString() : 'Never'
        }));
    }
    
    /**
     * Get performance insights
     */
    getPerformanceInsights() {
        const perf = this.analytics.performance;
        
        return {
            pageLoad: perf.pageLoad ? {
                domContentLoaded: Math.round(perf.pageLoad.domContentLoaded),
                loadComplete: Math.round(perf.pageLoad.loadComplete),
                totalTime: Math.round(perf.pageLoad.totalTime)
            } : null,
            
            resources: perf.resources ? {
                count: perf.resources.length,
                averageLoadTime: Math.round(
                    perf.resources.reduce((sum, r) => sum + r.duration, 0) / perf.resources.length
                ),
                totalSize: perf.resources.reduce((sum, r) => sum + r.size, 0)
            } : null,
            
            memory: perf.memory ? {
                used: Math.round(perf.memory.used / 1024 / 1024), // MB
                total: Math.round(perf.memory.total / 1024 / 1024), // MB
                percentage: Math.round((perf.memory.used / perf.memory.total) * 100)
            } : null
        };
    }
    
    /**
     * Export analytics data
     */
    exportAnalytics() {
        const data = {
            summary: this.getAnalyticsSummary(),
            languageInsights: this.getLanguageInsights(),
            performanceInsights: this.getPerformanceInsights(),
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `meddevice-analytics-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    /**
     * Clear analytics data
     */
    clearAnalytics() {
        this.analytics = {
            languageUsage: {},
            stageVisits: {},
            timeSpent: {},
            interactions: [],
            performance: {},
            userPreferences: {}
        };
        
        localStorage.removeItem('medDevice_analytics');
    }
}

// Initialize analytics and performance monitoring
let analyticsPerformance;

document.addEventListener('DOMContentLoaded', function() {
    analyticsPerformance = new AnalyticsPerformance();
    
    // Add analytics to global scope for debugging
    window.Analytics = analyticsPerformance;
    
    // Add analytics dashboard link to admin pages
    if (window.location.pathname.includes('content-manager.html')) {
        setTimeout(() => {
            addAnalyticsDashboardLink();
        }, 1000);
    }
});

/**
 * Add analytics dashboard link to admin pages
 */
function addAnalyticsDashboardLink() {
    const header = document.querySelector('.admin-header .col-md-4');
    if (header) {
        const analyticsBtn = document.createElement('button');
        analyticsBtn.className = 'btn btn-outline-light ms-2';
        analyticsBtn.innerHTML = '<i class="fas fa-chart-line me-2"></i>Analytics';
        analyticsBtn.onclick = () => showAnalyticsDashboard();
        header.appendChild(analyticsBtn);
    }
}

/**
 * Show analytics dashboard modal
 */
function showAnalyticsDashboard() {
    const insights = analyticsPerformance.getLanguageInsights();
    const performance = analyticsPerformance.getPerformanceInsights();
    
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Analytics Dashboard</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Language Usage</h6>
                            ${insights.map(lang => `
                                <div class="d-flex justify-content-between">
                                    <span>${lang.language.toUpperCase()}</span>
                                    <span>${lang.percentage}% (${lang.sessions} sessions)</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="col-md-6">
                            <h6>Performance</h6>
                            ${performance.pageLoad ? `
                                <div>Page Load: ${performance.pageLoad.totalTime}ms</div>
                                <div>DOM Ready: ${performance.pageLoad.domContentLoaded}ms</div>
                            ` : '<div>No performance data</div>'}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="analyticsPerformance.exportAnalytics()">Export Data</button>
                    <button class="btn btn-outline-danger" onclick="analyticsPerformance.clearAnalytics()">Clear Data</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// Export for global use
window.AnalyticsPerformance = AnalyticsPerformance;
