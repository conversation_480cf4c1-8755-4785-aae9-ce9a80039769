/**
 * Medical Device Innovation Guide - Internationalization (i18n) System
 * Handles language switching between English and Arabic
 */

// Translation dictionary
const translations = {
    en: {
        // Navigation
        "app_title": "Medical Device Innovation Guide",
        "nav_home": "Home",
        "nav_dashboard": "Dashboard",
        "nav_resources": "Resources",
        "nav_guide": "Guide",
        "nav_workspace": "Workspace",
        
        // Home Page
        "hero_title": "Medical Device Innovation Guide",
        "hero_subtitle": "An interactive academic journey from concept to market. Learn the complete process of medical device innovation through 20 comprehensive stages.",
        "start_learning_btn": "Start Learning",
        "resources_btn": "Resources",
        "why_this_guide": "Why This Guide?",
        "why_subtitle": "Comprehensive, interactive, and practical approach to medical device innovation",
        
        // Features
        "research_based": "Research-Based",
        "research_desc": "Built on latest research and industry best practices in medical device development.",
        "interactive_learning": "Interactive Learning",
        "interactive_desc": "Engage with interactive content, case studies, and practical exercises.",
        "progress_tracking": "Progress Tracking",
        "progress_desc": "Track your learning progress through all 20 stages of innovation.",
        
        // Stages Overview
        "stages_title": "20 Stages of Innovation",
        "stages_subtitle": "From initial concept to market success",
        "foundation": "Foundation",
        "foundation_desc": "Understanding the landscape and identifying opportunities",
        "development": "Development",
        "development_desc": "Idea generation, testing, and validation",
        "protection": "Protection",
        "protection_desc": "Patents, regulations, and business planning",
        "market": "Market",
        "market_desc": "Promotion, manufacturing, and continuous improvement",
        
        // Call to Action
        "cta_title": "Ready to Start Your Innovation Journey?",
        "cta_subtitle": "Join thousands of innovators who have transformed healthcare through medical device innovation.",
        "begin_journey_btn": "Begin Your Journey",
        
        // Dashboard
        "dashboard_title": "Learning Dashboard",
        "dashboard_subtitle": "Track your progress through the 20 stages of medical device innovation",
        "completed_stages": "Stages Completed",
        "time_spent": "Time Spent",
        "achievements": "Achievements",
        "projects_started": "Projects Started",
        "all_stages": "All Stages",
        "recent_activity": "Recent Activity",
        "your_progress": "Your Progress",
        "quick_navigation": "Quick Navigation",
        
        // Stage Navigation
        "previous": "Previous",
        "next": "Next",
        "complete": "Complete",
        "learning_objectives": "Learning Objectives",
        "key_takeaways": "Key Takeaways",
        "interactive_exercise": "Interactive Exercise",
        "save_reflections": "Save Reflections",
        "related_resources": "Related Resources",
        
        // Stage Titles
        "stage_1_title": "General Introduction",
        "stage_2_title": "Medical Device Landscape",
        "stage_3_title": "Innovation Process",
        "stage_4_title": "Understanding Opportunity",
        "stage_5_title": "Needs Discovery",
        "stage_6_title": "Idea Generation",
        "stage_7_title": "Testing Your Idea",
        "stage_8_title": "Clinical Trials",
        "stage_9_title": "Reliability Considerations",
        "stage_10_title": "Innovation Notebooks",
        "stage_11_title": "Patent Fundamentals",
        "stage_12_title": "Regulatory Strategy",
        "stage_13_title": "Reimbursement Fundamentals",
        "stage_14_title": "Funding Your Idea",
        "stage_15_title": "Business Plans",
        "stage_16_title": "Promoting Your Idea",
        "stage_17_title": "Resources",
        "stage_18_title": "Risk Management",
        "stage_19_title": "Manufacturing & Supply Chain",
        "stage_20_title": "Post-Market Monitoring",
        
        // Resources Page
        "resources_title": "Resources & Tools",
        "resources_subtitle": "Comprehensive collection of tools, templates, and references for medical device innovation",
        "templates_tools": "Templates & Tools",
        "regulatory_resources": "Regulatory Resources",
        "learning_materials": "Learning Materials",
        "interactive_tools": "Interactive Tools",
        "quick_links": "Quick Links",
        
        // Interactive Tools
        "cost_calculator": "Development Cost Calculator",
        "cost_calc_desc": "Estimate the costs involved in developing your medical device from concept to market.",
        "launch_calculator": "Launch Calculator",
        "timeline_planner": "Project Timeline Planner",
        "timeline_desc": "Create a realistic timeline for your medical device development project.",
        "plan_timeline": "Plan Timeline",
        "risk_assessment": "Risk Assessment Tool",
        "risk_desc": "Identify and assess potential risks in your medical device project.",
        "assess_risks": "Assess Risks",
        "market_analysis": "Market Analysis Tool",
        "market_desc": "Analyze market opportunities and competitive landscape for your device.",
        "analyze_market": "Analyze Market",
        
        // Common Actions
        "start": "Start",
        "review": "Review",
        "download": "Download",
        "save": "Save",
        "cancel": "Cancel",
        "submit": "Submit",
        "continue": "Continue",
        "back": "Back",
        "close": "Close",
        "edit": "Edit",
        "delete": "Delete",
        "view": "View",
        "help": "Help",
        
        // Form Labels
        "name": "Name",
        "email": "Email",
        "description": "Description",
        "notes": "Notes",
        "comments": "Comments",
        "feedback": "Feedback",
        "search": "Search",
        "filter": "Filter",
        "sort": "Sort",
        
        // Status Messages
        "loading": "Loading...",
        "saving": "Saving...",
        "saved": "Saved successfully",
        "error": "An error occurred",
        "success": "Success",
        "warning": "Warning",
        "info": "Information",
        
        // Footer
        "footer_text": "Empowering the next generation of medical innovators.",
        "copyright": "All rights reserved.",
        
        // Accessibility
        "skip_to_content": "Skip to main content",
        "menu_toggle": "Toggle navigation menu",
        "language_toggle": "Switch language",

        // Advanced Demo Features
        "advanced_demo_btn": "Advanced Demo",
        "advanced_demo_title": "Advanced Bilingual Features Demo",
        "demo_subtitle": "Experience the full power of our bilingual medical device innovation platform",
        "voice_narration": "Voice Narration",
        "voice_demo_desc": "Experience our advanced text-to-speech system with support for both English and Arabic.",
        "voice_demo_text": "Medical device innovation requires a systematic approach to transform healthcare challenges into breakthrough solutions. Our comprehensive guide provides the framework and tools needed for successful innovation.",
        "play_narration": "Play Narration",
        "stop_narration": "Stop",
        "advanced_i18n": "Advanced Internationalization",
        "i18n_demo_desc": "See how our system handles numbers, dates, currencies, and cultural adaptations.",
        "currency_formatting": "Currency Formatting",
        "date_formatting": "Date Formatting",
        "percentage_formatting": "Percentage Formatting",
        "time_formatting": "Time Formatting",
        "project_start": "Project Start",
        "milestone_date": "Milestone",
        "launch_date": "Launch",
        "success_rate": "Success Rate",
        "completion_rate": "Completion",
        "last_activity": "Last Activity",
        "next_review": "Next Review",
        "content_management": "Content Management",
        "content_demo_desc": "Manage bilingual content with our advanced content management system.",
        "dynamic_content": "Dynamic Content Rendering",
        "load_content": "Load Content",
        "open_cms": "Open CMS",
        "analytics": "Analytics",
        "language_usage": "Language Usage",
        "page_views": "Page Views",
        "avg_session": "Avg Session",
        "view_details": "View Details",
        "performance": "Performance Features",
        "performance_demo_desc": "Experience our optimized loading and caching systems.",
        "lazy_loading": "Lazy Loading",
        "lazy_loading_desc": "Content loads on demand for faster performance",
        "smart_caching": "Smart Caching",
        "caching_desc": "Intelligent caching reduces load times",
        "real_time_analytics": "Real-time Analytics",
        "analytics_desc": "Monitor usage and performance in real-time",
        "explore_more": "Explore More",
        "start_learning": "Start Learning",
        "global_market": "Global Market",
        "growth_rate": "Growth Rate",
        "total_stages": "Learning Stages",
        "last_updated": "Last Updated"
    },
    
    ar: {
        // Navigation
        "app_title": "دليل الابتكار في الأجهزة الطبية",
        "nav_home": "الرئيسية",
        "nav_dashboard": "لوحة التحكم",
        "nav_resources": "الموارد",
        "nav_guide": "الدليل",
        "nav_workspace": "مساحة العمل",
        
        // Home Page
        "hero_title": "دليل الابتكار في الأجهزة الطبية",
        "hero_subtitle": "رحلة أكاديمية تفاعلية من الفكرة إلى السوق. تعلم العملية الكاملة لابتكار الأجهزة الطبية من خلال 20 مرحلة شاملة.",
        "start_learning_btn": "ابدأ التعلم",
        "resources_btn": "الموارد",
        "why_this_guide": "لماذا هذا الدليل؟",
        "why_subtitle": "نهج شامل وتفاعلي وعملي لابتكار الأجهزة الطبية",
        
        // Features
        "research_based": "قائم على البحث",
        "research_desc": "مبني على أحدث الأبحاث وأفضل الممارسات في تطوير الأجهزة الطبية.",
        "interactive_learning": "تعلم تفاعلي",
        "interactive_desc": "تفاعل مع المحتوى التفاعلي ودراسات الحالة والتمارين العملية.",
        "progress_tracking": "تتبع التقدم",
        "progress_desc": "تتبع تقدمك في التعلم عبر جميع مراحل الابتكار العشرين.",
        
        // Stages Overview
        "stages_title": "20 مرحلة من الابتكار",
        "stages_subtitle": "من الفكرة الأولية إلى نجاح السوق",
        "foundation": "الأساس",
        "foundation_desc": "فهم المشهد وتحديد الفرص",
        "development": "التطوير",
        "development_desc": "توليد الأفكار والاختبار والتحقق",
        "protection": "الحماية",
        "protection_desc": "براءات الاختراع واللوائح وتخطيط الأعمال",
        "market": "السوق",
        "market_desc": "الترويج والتصنيع والتحسين المستمر",
        
        // Call to Action
        "cta_title": "هل أنت مستعد لبدء رحلة الابتكار؟",
        "cta_subtitle": "انضم إلى آلاف المبتكرين الذين غيروا الرعاية الصحية من خلال ابتكار الأجهزة الطبية.",
        "begin_journey_btn": "ابدأ رحلتك",
        
        // Dashboard
        "dashboard_title": "لوحة تحكم التعلم",
        "dashboard_subtitle": "تتبع تقدمك عبر مراحل ابتكار الأجهزة الطبية العشرين",
        "completed_stages": "المراحل المكتملة",
        "time_spent": "الوقت المستغرق",
        "achievements": "الإنجازات",
        "projects_started": "المشاريع المبدوءة",
        "all_stages": "جميع المراحل",
        "recent_activity": "النشاط الأخير",
        "your_progress": "تقدمك",
        "quick_navigation": "التنقل السريع",
        
        // Stage Navigation
        "previous": "السابق",
        "next": "التالي",
        "complete": "إكمال",
        "learning_objectives": "أهداف التعلم",
        "key_takeaways": "النقاط الرئيسية",
        "interactive_exercise": "تمرين تفاعلي",
        "save_reflections": "حفظ التأملات",
        "related_resources": "الموارد ذات الصلة",
        
        // Stage Titles
        "stage_1_title": "مقدمة عامة",
        "stage_2_title": "مشهد الأجهزة الطبية",
        "stage_3_title": "عملية الابتكار",
        "stage_4_title": "فهم الفرصة",
        "stage_5_title": "اكتشاف الاحتياجات",
        "stage_6_title": "توليد الأفكار",
        "stage_7_title": "اختبار فكرتك",
        "stage_8_title": "التجارب السريرية",
        "stage_9_title": "اعتبارات الموثوقية",
        "stage_10_title": "دفاتر الابتكار",
        "stage_11_title": "أساسيات براءات الاختراع",
        "stage_12_title": "الاستراتيجية التنظيمية",
        "stage_13_title": "أساسيات السداد",
        "stage_14_title": "تمويل فكرتك",
        "stage_15_title": "خطط الأعمال",
        "stage_16_title": "الترويج لفكرتك",
        "stage_17_title": "الموارد",
        "stage_18_title": "إدارة المخاطر",
        "stage_19_title": "التصنيع وسلسلة التوريد",
        "stage_20_title": "المراقبة بعد التسويق",
        
        // Resources Page
        "resources_title": "الموارد والأدوات",
        "resources_subtitle": "مجموعة شاملة من الأدوات والقوالب والمراجع لابتكار الأجهزة الطبية",
        "templates_tools": "القوالب والأدوات",
        "regulatory_resources": "الموارد التنظيمية",
        "learning_materials": "مواد التعلم",
        "interactive_tools": "الأدوات التفاعلية",
        "quick_links": "روابط سريعة",
        
        // Interactive Tools
        "cost_calculator": "حاسبة تكلفة التطوير",
        "cost_calc_desc": "تقدير التكاليف المتضمنة في تطوير جهازك الطبي من الفكرة إلى السوق.",
        "launch_calculator": "تشغيل الحاسبة",
        "timeline_planner": "مخطط الجدول الزمني للمشروع",
        "timeline_desc": "إنشاء جدول زمني واقعي لمشروع تطوير جهازك الطبي.",
        "plan_timeline": "تخطيط الجدول الزمني",
        "risk_assessment": "أداة تقييم المخاطر",
        "risk_desc": "تحديد وتقييم المخاطر المحتملة في مشروع جهازك الطبي.",
        "assess_risks": "تقييم المخاطر",
        "market_analysis": "أداة تحليل السوق",
        "market_desc": "تحليل فرص السوق والمشهد التنافسي لجهازك.",
        "analyze_market": "تحليل السوق",
        
        // Common Actions
        "start": "ابدأ",
        "review": "مراجعة",
        "download": "تحميل",
        "save": "حفظ",
        "cancel": "إلغاء",
        "submit": "إرسال",
        "continue": "متابعة",
        "back": "رجوع",
        "close": "إغلاق",
        "edit": "تحرير",
        "delete": "حذف",
        "view": "عرض",
        "help": "مساعدة",
        
        // Form Labels
        "name": "الاسم",
        "email": "البريد الإلكتروني",
        "description": "الوصف",
        "notes": "ملاحظات",
        "comments": "تعليقات",
        "feedback": "ملاحظات",
        "search": "بحث",
        "filter": "تصفية",
        "sort": "ترتيب",
        
        // Status Messages
        "loading": "جاري التحميل...",
        "saving": "جاري الحفظ...",
        "saved": "تم الحفظ بنجاح",
        "error": "حدث خطأ",
        "success": "نجح",
        "warning": "تحذير",
        "info": "معلومات",
        
        // Footer
        "footer_text": "تمكين الجيل القادم من المبتكرين الطبيين.",
        "copyright": "جميع الحقوق محفوظة.",
        
        // Accessibility
        "skip_to_content": "انتقل إلى المحتوى الرئيسي",
        "menu_toggle": "تبديل قائمة التنقل",
        "language_toggle": "تبديل اللغة"
    }
};

// Current language state
let currentLanguage = 'en';

/**
 * Initialize the i18n system
 */
function initializeI18n() {
    // Detect browser language preference
    const browserLang = navigator.language || navigator.userLanguage;
    const defaultLang = browserLang.startsWith('ar') ? 'ar' : 'en';
    
    // Load saved language preference or use default
    const savedLang = localStorage.getItem('medDevice_language') || defaultLang;
    
    // Set initial language
    setLanguage(savedLang);
    
    // Update language toggle UI
    updateLanguageToggle();
}

/**
 * Set the application language
 * @param {string} lang - Language code ('en' or 'ar')
 */
function setLanguage(lang) {
    if (!translations[lang]) {
        console.warn(`Language '${lang}' not supported. Falling back to English.`);
        lang = 'en';
    }
    
    currentLanguage = lang;
    
    // Set HTML direction and language attributes
    const htmlElement = document.documentElement;
    htmlElement.setAttribute('lang', lang);
    htmlElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');
    
    // Update all translatable elements
    updateTranslatableElements(lang);
    
    // Save language preference
    localStorage.setItem('medDevice_language', lang);
    
    // Update language toggle UI
    updateLanguageToggle();
    
    // Trigger custom event for other components
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }));
    
    console.log(`Language set to: ${lang}`);
}

/**
 * Update all elements with data-i18n-key attributes
 * @param {string} lang - Language code
 */
function updateTranslatableElements(lang) {
    const elements = document.querySelectorAll('[data-i18n-key]');
    
    elements.forEach(element => {
        const key = element.getAttribute('data-i18n-key');
        const translation = translations[lang][key];
        
        if (translation) {
            // Handle different types of elements
            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'email')) {
                element.placeholder = translation;
            } else if (element.tagName === 'TEXTAREA') {
                element.placeholder = translation;
            } else if (element.hasAttribute('title')) {
                element.title = translation;
            } else {
                element.innerHTML = translation;
            }
        } else {
            console.warn(`Translation not found for key: ${key} in language: ${lang}`);
        }
    });
}

/**
 * Update the language toggle button
 */
function updateLanguageToggle() {
    const toggleButton = document.getElementById('languageToggle');
    if (toggleButton) {
        const isArabic = currentLanguage === 'ar';
        toggleButton.innerHTML = `
            <i class="fas fa-globe me-2"></i>
            <span class="current-lang">${isArabic ? 'ع' : 'EN'}</span>
            <span class="separator">|</span>
            <span class="other-lang">${isArabic ? 'EN' : 'ع'}</span>
        `;
        toggleButton.setAttribute('title', translations[currentLanguage]['language_toggle']);
    }
}

/**
 * Toggle between English and Arabic
 */
function toggleLanguage() {
    const newLang = currentLanguage === 'en' ? 'ar' : 'en';
    setLanguage(newLang);
}

/**
 * Get current language
 * @returns {string} Current language code
 */
function getCurrentLanguage() {
    return currentLanguage;
}

/**
 * Get translation for a specific key
 * @param {string} key - Translation key
 * @param {string} lang - Language code (optional, uses current language if not provided)
 * @returns {string} Translated text
 */
function getTranslation(key, lang = currentLanguage) {
    return translations[lang] && translations[lang][key] ? translations[lang][key] : key;
}

/**
 * Add event listeners for language toggle
 */
function setupLanguageToggle() {
    // Add click event to language toggle button
    document.addEventListener('click', function(event) {
        if (event.target.closest('#languageToggle')) {
            event.preventDefault();
            toggleLanguage();
        }
    });
    
    // Add keyboard support for language toggle
    document.addEventListener('keydown', function(event) {
        if (event.target.closest('#languageToggle') && (event.key === 'Enter' || event.key === ' ')) {
            event.preventDefault();
            toggleLanguage();
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeI18n();
    setupLanguageToggle();
});

// Export functions for use in other modules
window.I18n = {
    setLanguage,
    getCurrentLanguage,
    getTranslation,
    toggleLanguage,
    translations
};
