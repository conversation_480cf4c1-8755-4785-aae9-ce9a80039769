/**
 * Medical Device Innovation Guide - Layout Manager
 * Dynamic layout fixes and overlap prevention
 */

class LayoutManager {
    constructor() {
        this.navHeight = 60;
        this.isRTL = document.documentElement.dir === 'rtl';
        this.init();
    }

    /**
     * Initialize layout management
     */
    init() {
        this.fixInitialLayout();
        this.setupResizeHandler();
        this.setupScrollHandler();
        this.fixNavigationOverlap();
        this.adjustContentSpacing();
        this.handleMobileLayout();
        
        // Run fixes after DOM is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.runAllFixes();
            });
        } else {
            this.runAllFixes();
        }
    }

    /**
     * Fix initial layout issues
     */
    fixInitialLayout() {
        // Ensure body has proper padding for fixed navigation
        document.body.style.paddingTop = this.navHeight + 'px';
        
        // Fix main content areas
        const mainElements = document.querySelectorAll('main, .main-content, .hero-section, .showcase-header');
        mainElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Fix navigation overlap issues
     */
    fixNavigationOverlap() {
        const nav = document.querySelector('.enhanced-navigation');
        if (nav) {
            nav.style.position = 'fixed';
            nav.style.top = '0';
            nav.style.left = '0';
            nav.style.right = '0';
            nav.style.zIndex = '1050';
        }

        // Fix search and bookmark panels
        const panels = document.querySelectorAll('.search-panel, .bookmarks-panel');
        panels.forEach(panel => {
            panel.style.position = 'relative';
            panel.style.zIndex = '1049';
        });
    }

    /**
     * Adjust content spacing
     */
    adjustContentSpacing() {
        // Fix hero sections
        const heroSections = document.querySelectorAll('.hero-section');
        heroSections.forEach(section => {
            section.style.marginTop = '0';
            section.style.paddingTop = '2rem';
            
            // Adjust min-height for viewport
            const minVhElements = section.querySelectorAll('.min-vh-100');
            minVhElements.forEach(element => {
                element.style.minHeight = `calc(100vh - ${this.navHeight}px)`;
            });
        });

        // Fix showcase headers
        const showcaseHeaders = document.querySelectorAll('.showcase-header');
        showcaseHeaders.forEach(header => {
            header.style.marginTop = '0';
            header.style.paddingTop = '3rem';
        });

        // Fix first sections
        const firstSections = document.querySelectorAll('section:first-of-type');
        firstSections.forEach(section => {
            section.style.marginTop = '0';
        });
    }

    /**
     * Handle mobile layout adjustments
     */
    handleMobileLayout() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            document.body.style.paddingTop = '70px';
            
            // Adjust floating actions
            const fab = document.querySelector('.floating-actions');
            if (fab) {
                fab.style.bottom = '1rem';
                fab.style.right = this.isRTL ? 'auto' : '1rem';
                fab.style.left = this.isRTL ? '1rem' : 'auto';
            }
        }
    }

    /**
     * Setup window resize handler
     */
    setupResizeHandler() {
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleMobileLayout();
                this.adjustContentSpacing();
            }, 250);
        });
    }

    /**
     * Setup scroll handler for dynamic adjustments
     */
    setupScrollHandler() {
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.handleScrollEffects();
            }, 100);
        });
    }

    /**
     * Handle scroll-based effects
     */
    handleScrollEffects() {
        const scrollY = window.scrollY;
        
        // Adjust navigation opacity based on scroll
        const nav = document.querySelector('.enhanced-nav-bar');
        if (nav) {
            if (scrollY > 50) {
                nav.style.backgroundColor = 'rgba(102, 126, 234, 0.95)';
                nav.style.backdropFilter = 'blur(10px)';
            } else {
                nav.style.backgroundColor = '';
                nav.style.backdropFilter = '';
            }
        }
    }

    /**
     * Fix z-index issues
     */
    fixZIndexIssues() {
        const zIndexMap = {
            '.enhanced-navigation': 1050,
            '.search-panel': 1049,
            '.bookmarks-panel': 1049,
            '.breadcrumb-container': 1048,
            '.modal': 1055,
            '.modal-backdrop': 1054,
            '.dropdown-menu': 1060,
            '.tooltip': 1070,
            '.popover': 1065,
            '.floating-actions': 1030,
            '.nav-menu': 1045
        };

        Object.entries(zIndexMap).forEach(([selector, zIndex]) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.style.zIndex = zIndex;
                element.style.position = element.style.position || 'relative';
            });
        });
    }

    /**
     * Fix content overlap
     */
    fixContentOverlap() {
        const contentElements = document.querySelectorAll(`
            .container, .container-fluid, .card, .workspace-section,
            .integration-card, .feature-card, .exercise-card,
            .assessment-matrix, .swot-matrix, .brainstorming-area,
            .test-designer, .study-design, .fmea-builder
        `);

        contentElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Fix form elements
     */
    fixFormElements() {
        const formElements = document.querySelectorAll(`
            .form-control, .form-select, .btn, .input-group,
            .accordion, .accordion-item, .table-responsive
        `);

        formElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Fix Tailwind CSS conflicts
     */
    fixTailwindConflicts() {
        const tailwindElements = document.querySelectorAll(`
            .bg-white, .rounded-lg, .shadow-lg, .shadow-md,
            .bg-gradient-to-br, .transition-all
        `);

        tailwindElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Run all layout fixes
     */
    runAllFixes() {
        try {
            this.fixZIndexIssues();
            this.fixContentOverlap();
            this.fixFormElements();
            this.fixTailwindConflicts();
            this.adjustContentSpacing();
            
            // Add utility classes
            this.addUtilityClasses();
            
            console.log('Layout Manager: All fixes applied successfully');
        } catch (error) {
            console.warn('Layout Manager: Some fixes failed', error);
        }
    }

    /**
     * Add utility classes for manual fixes
     */
    addUtilityClasses() {
        const style = document.createElement('style');
        style.textContent = `
            .fix-z-index { position: relative !important; z-index: 1 !important; }
            .fix-z-index-high { position: relative !important; z-index: 10 !important; }
            .fix-no-overlap { position: relative !important; z-index: 1 !important; margin-top: 0 !important; }
            .fix-spacing-top { margin-top: 1rem !important; }
            .fix-spacing-bottom { margin-bottom: 1rem !important; }
        `;
        document.head.appendChild(style);
    }

    /**
     * Fix specific page layouts
     */
    fixPageSpecific() {
        const currentPage = this.detectCurrentPage();
        
        switch (currentPage) {
            case 'index':
                this.fixHomePage();
                break;
            case 'dashboard':
                this.fixDashboardPage();
                break;
            case 'interactive-book':
                this.fixInteractiveBookPage();
                break;
            case 'integration-showcase':
                this.fixShowcasePage();
                break;
            default:
                this.fixGenericPage();
        }
    }

    /**
     * Detect current page
     */
    detectCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';
        
        if (filename.includes('dashboard')) return 'dashboard';
        if (filename.includes('Interactive Academic Book')) return 'interactive-book';
        if (filename.includes('integration-showcase')) return 'integration-showcase';
        if (filename === 'index.html' || filename === '') return 'index';
        
        return 'generic';
    }

    /**
     * Fix home page specific issues
     */
    fixHomePage() {
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            heroSection.style.paddingTop = '2rem';
            
            const minVhElements = heroSection.querySelectorAll('.min-vh-100');
            minVhElements.forEach(element => {
                element.style.minHeight = `calc(100vh - ${this.navHeight}px)`;
            });
        }
    }

    /**
     * Fix dashboard page specific issues
     */
    fixDashboardPage() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.style.position = 'relative';
            sidebar.style.zIndex = '1';
        }

        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.style.paddingTop = '1rem';
        }
    }

    /**
     * Fix interactive book page specific issues
     */
    fixInteractiveBookPage() {
        const containers = document.querySelectorAll('.container, .mx-auto');
        containers.forEach(container => {
            container.style.position = 'relative';
            container.style.zIndex = '1';
        });
    }

    /**
     * Fix showcase page specific issues
     */
    fixShowcasePage() {
        const showcaseHeader = document.querySelector('.showcase-header');
        if (showcaseHeader) {
            showcaseHeader.style.marginTop = '0';
            showcaseHeader.style.paddingTop = '3rem';
        }
    }

    /**
     * Fix generic page issues
     */
    fixGenericPage() {
        const mainElements = document.querySelectorAll('main, .main-content, section');
        mainElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Public method to manually trigger fixes
     */
    static applyFixes() {
        const manager = new LayoutManager();
        manager.runAllFixes();
        manager.fixPageSpecific();
    }
}

// Initialize layout manager
let layoutManager;

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        layoutManager = new LayoutManager();
    });
} else {
    layoutManager = new LayoutManager();
}

// Make it globally available
window.LayoutManager = LayoutManager;
window.layoutManager = layoutManager;

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LayoutManager;
}

/**
 * Interactive Home Page Functions
 */

// Navigation functions for interactive elements
function navigateToStage(stageNumber) {
    // Track analytics
    if (window.analyticsPerformance) {
        window.analyticsPerformance.trackInteraction('stage_navigation', {
            stage: stageNumber,
            source: 'home_page'
        });
    }

    // Determine the correct stage file
    let stageFile;
    if (stageNumber <= 10) {
        stageFile = `stage-${stageNumber.toString().padStart(2, '0')}-bilingual.html`;
    } else {
        stageFile = `stage-${stageNumber}.html`; // For future stages
    }

    // Add loading animation
    showNavigationLoading();

    // Navigate with smooth transition
    setTimeout(() => {
        window.location.href = stageFile;
    }, 500);
}

function navigateToStageGroup(startStage) {
    // Navigate to the first stage of the group
    navigateToStage(startStage);
}

function previewStageGroup(startStage) {
    // Show preview modal or panel
    showStageGroupPreview(startStage);
}

function showStageGroupPreview(startStage) {
    const previewData = {
        1: {
            title: 'Foundation Stages (1-5)',
            description: 'Learn the fundamentals of medical device innovation',
            stages: [
                { number: 1, title: 'Introduction', description: 'Overview of the innovation process' },
                { number: 2, title: 'Market Landscape', description: 'Understanding the market environment' },
                { number: 3, title: 'Innovation Process', description: 'Process mapping and stage-gate framework' },
                { number: 4, title: 'Understanding Opportunity', description: 'Opportunity assessment and SWOT analysis' },
                { number: 5, title: 'Needs Discovery', description: 'Stakeholder mapping and needs prioritization' }
            ]
        },
        6: {
            title: 'Development Stages (6-10)',
            description: 'Generate, test, and validate your ideas',
            stages: [
                { number: 6, title: 'Idea Generation', description: 'Brainstorming and concept development' },
                { number: 7, title: 'Testing Your Idea', description: 'Prototype planning and testing protocols' },
                { number: 8, title: 'Clinical Trials', description: 'Study design and regulatory planning' },
                { number: 9, title: 'Reliability Considerations', description: 'FMEA analysis and reliability testing' },
                { number: 10, title: 'Innovation Notebooks', description: 'Documentation and record keeping' }
            ]
        }
        // Add more groups as needed
    };

    const data = previewData[startStage];
    if (!data) return;

    // Create and show modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${data.title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="lead">${data.description}</p>
                    <div class="row">
                        ${data.stages.map(stage => `
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <span class="badge bg-primary me-2">${stage.number}</span>
                                            ${stage.title}
                                        </h6>
                                        <p class="card-text small">${stage.description}</p>
                                        <button class="btn btn-sm btn-outline-primary" onclick="navigateToStage(${stage.number})">
                                            Start Stage
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="navigateToStage(${startStage})">
                        Start from Stage ${startStage}
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Remove modal after hiding
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function showNavigationLoading() {
    // Create loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'navigation-loading';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(102, 126, 234, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-size: 1.2rem;
    `;
    overlay.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-light mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>Loading Stage...</div>
        </div>
    `;

    document.body.appendChild(overlay);
}

// Initialize interactive elements
function initializeInteractiveHomePage() {
    // Add hover effects to interactive buttons
    const interactiveButtons = document.querySelectorAll('.interactive-btn');
    interactiveButtons.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add click effects to stage cards
    const stageCards = document.querySelectorAll('.interactive-stage-card');
    stageCards.forEach(card => {
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // Initialize orbit item interactions
    const orbitItems = document.querySelectorAll('.orbit-item');
    orbitItems.forEach(item => {
        item.addEventListener('click', function() {
            const stage = this.dataset.stage;
            if (stage) {
                navigateToStage(parseInt(stage));
            }
        });
    });

    // Initialize timeline stage interactions
    const timelineStages = document.querySelectorAll('.timeline-stage');
    timelineStages.forEach(stage => {
        stage.addEventListener('mouseenter', function() {
            this.querySelector('.stage-dot').style.transform = 'scale(1.1)';
        });

        stage.addEventListener('mouseleave', function() {
            this.querySelector('.stage-dot').style.transform = '';
        });
    });

    // Load and display progress
    loadStageProgress();

    // Initialize animations
    initializeAnimations();
}

function loadStageProgress() {
    // Load progress from localStorage or analytics
    const progress = JSON.parse(localStorage.getItem('stageProgress') || '{}');

    // Update progress bars
    document.querySelectorAll('.stage-group-card').forEach(card => {
        const stages = card.dataset.stages;
        if (stages) {
            const [start, end] = stages.split('-').map(Number);
            let completedStages = 0;
            let totalStages = end - start + 1;

            for (let i = start; i <= end; i++) {
                if (progress[i]) {
                    completedStages++;
                }
            }

            const percentage = (completedStages / totalStages) * 100;
            const progressBar = card.querySelector('.progress-bar');
            const progressText = card.querySelector('.progress + small');

            if (progressBar) {
                progressBar.style.width = percentage + '%';
            }
            if (progressText) {
                progressText.textContent = `${Math.round(percentage)}% Complete`;
            }
        }
    });

    // Update timeline stages
    document.querySelectorAll('.timeline-stage').forEach(stage => {
        const stageNumber = parseInt(stage.dataset.stage);
        if (progress[stageNumber]) {
            stage.classList.add('completed');
            const dot = stage.querySelector('.stage-dot');
            if (dot) {
                dot.style.background = '#28a745';
                dot.style.color = 'white';
                dot.innerHTML = '<i class="fas fa-check"></i>';
            }
        }
    });
}

function initializeAnimations() {
    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, { threshold: 0.1 });

    // Observe animated elements
    document.querySelectorAll('.stage-group-card, .timeline-stage').forEach(el => {
        observer.observe(el);
    });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
        initializeInteractiveHomePage();
    }
});

// Make functions globally available
window.navigateToStage = navigateToStage;
window.navigateToStageGroup = navigateToStageGroup;
window.previewStageGroup = previewStageGroup;
