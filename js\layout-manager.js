/**
 * Medical Device Innovation Guide - Layout Manager
 * Dynamic layout fixes and overlap prevention
 */

class LayoutManager {
    constructor() {
        this.navHeight = 60;
        this.isRTL = document.documentElement.dir === 'rtl';
        this.init();
    }

    /**
     * Initialize layout management
     */
    init() {
        this.fixInitialLayout();
        this.setupResizeHandler();
        this.setupScrollHandler();
        this.fixNavigationOverlap();
        this.adjustContentSpacing();
        this.handleMobileLayout();
        
        // Run fixes after DOM is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.runAllFixes();
            });
        } else {
            this.runAllFixes();
        }
    }

    /**
     * Fix initial layout issues
     */
    fixInitialLayout() {
        // Ensure body has proper padding for fixed navigation
        document.body.style.paddingTop = this.navHeight + 'px';
        
        // Fix main content areas
        const mainElements = document.querySelectorAll('main, .main-content, .hero-section, .showcase-header');
        mainElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Fix navigation overlap issues
     */
    fixNavigationOverlap() {
        const nav = document.querySelector('.enhanced-navigation');
        if (nav) {
            nav.style.position = 'fixed';
            nav.style.top = '0';
            nav.style.left = '0';
            nav.style.right = '0';
            nav.style.zIndex = '1050';
        }

        // Fix search and bookmark panels
        const panels = document.querySelectorAll('.search-panel, .bookmarks-panel');
        panels.forEach(panel => {
            panel.style.position = 'relative';
            panel.style.zIndex = '1049';
        });
    }

    /**
     * Adjust content spacing
     */
    adjustContentSpacing() {
        // Fix hero sections
        const heroSections = document.querySelectorAll('.hero-section');
        heroSections.forEach(section => {
            section.style.marginTop = '0';
            section.style.paddingTop = '2rem';
            
            // Adjust min-height for viewport
            const minVhElements = section.querySelectorAll('.min-vh-100');
            minVhElements.forEach(element => {
                element.style.minHeight = `calc(100vh - ${this.navHeight}px)`;
            });
        });

        // Fix showcase headers
        const showcaseHeaders = document.querySelectorAll('.showcase-header');
        showcaseHeaders.forEach(header => {
            header.style.marginTop = '0';
            header.style.paddingTop = '3rem';
        });

        // Fix first sections
        const firstSections = document.querySelectorAll('section:first-of-type');
        firstSections.forEach(section => {
            section.style.marginTop = '0';
        });
    }

    /**
     * Handle mobile layout adjustments
     */
    handleMobileLayout() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            document.body.style.paddingTop = '70px';
            
            // Adjust floating actions
            const fab = document.querySelector('.floating-actions');
            if (fab) {
                fab.style.bottom = '1rem';
                fab.style.right = this.isRTL ? 'auto' : '1rem';
                fab.style.left = this.isRTL ? '1rem' : 'auto';
            }
        }
    }

    /**
     * Setup window resize handler
     */
    setupResizeHandler() {
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleMobileLayout();
                this.adjustContentSpacing();
            }, 250);
        });
    }

    /**
     * Setup scroll handler for dynamic adjustments
     */
    setupScrollHandler() {
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.handleScrollEffects();
            }, 100);
        });
    }

    /**
     * Handle scroll-based effects
     */
    handleScrollEffects() {
        const scrollY = window.scrollY;
        
        // Adjust navigation opacity based on scroll
        const nav = document.querySelector('.enhanced-nav-bar');
        if (nav) {
            if (scrollY > 50) {
                nav.style.backgroundColor = 'rgba(102, 126, 234, 0.95)';
                nav.style.backdropFilter = 'blur(10px)';
            } else {
                nav.style.backgroundColor = '';
                nav.style.backdropFilter = '';
            }
        }
    }

    /**
     * Fix z-index issues
     */
    fixZIndexIssues() {
        const zIndexMap = {
            '.enhanced-navigation': 1050,
            '.search-panel': 1049,
            '.bookmarks-panel': 1049,
            '.breadcrumb-container': 1048,
            '.modal': 1055,
            '.modal-backdrop': 1054,
            '.dropdown-menu': 1060,
            '.tooltip': 1070,
            '.popover': 1065,
            '.floating-actions': 1030,
            '.nav-menu': 1045
        };

        Object.entries(zIndexMap).forEach(([selector, zIndex]) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.style.zIndex = zIndex;
                element.style.position = element.style.position || 'relative';
            });
        });
    }

    /**
     * Fix content overlap
     */
    fixContentOverlap() {
        const contentElements = document.querySelectorAll(`
            .container, .container-fluid, .card, .workspace-section,
            .integration-card, .feature-card, .exercise-card,
            .assessment-matrix, .swot-matrix, .brainstorming-area,
            .test-designer, .study-design, .fmea-builder
        `);

        contentElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Fix form elements
     */
    fixFormElements() {
        const formElements = document.querySelectorAll(`
            .form-control, .form-select, .btn, .input-group,
            .accordion, .accordion-item, .table-responsive
        `);

        formElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Fix Tailwind CSS conflicts
     */
    fixTailwindConflicts() {
        const tailwindElements = document.querySelectorAll(`
            .bg-white, .rounded-lg, .shadow-lg, .shadow-md,
            .bg-gradient-to-br, .transition-all
        `);

        tailwindElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Run all layout fixes
     */
    runAllFixes() {
        try {
            this.fixZIndexIssues();
            this.fixContentOverlap();
            this.fixFormElements();
            this.fixTailwindConflicts();
            this.adjustContentSpacing();
            
            // Add utility classes
            this.addUtilityClasses();
            
            console.log('Layout Manager: All fixes applied successfully');
        } catch (error) {
            console.warn('Layout Manager: Some fixes failed', error);
        }
    }

    /**
     * Add utility classes for manual fixes
     */
    addUtilityClasses() {
        const style = document.createElement('style');
        style.textContent = `
            .fix-z-index { position: relative !important; z-index: 1 !important; }
            .fix-z-index-high { position: relative !important; z-index: 10 !important; }
            .fix-no-overlap { position: relative !important; z-index: 1 !important; margin-top: 0 !important; }
            .fix-spacing-top { margin-top: 1rem !important; }
            .fix-spacing-bottom { margin-bottom: 1rem !important; }
        `;
        document.head.appendChild(style);
    }

    /**
     * Fix specific page layouts
     */
    fixPageSpecific() {
        const currentPage = this.detectCurrentPage();
        
        switch (currentPage) {
            case 'index':
                this.fixHomePage();
                break;
            case 'dashboard':
                this.fixDashboardPage();
                break;
            case 'interactive-book':
                this.fixInteractiveBookPage();
                break;
            case 'integration-showcase':
                this.fixShowcasePage();
                break;
            default:
                this.fixGenericPage();
        }
    }

    /**
     * Detect current page
     */
    detectCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';
        
        if (filename.includes('dashboard')) return 'dashboard';
        if (filename.includes('Interactive Academic Book')) return 'interactive-book';
        if (filename.includes('integration-showcase')) return 'integration-showcase';
        if (filename === 'index.html' || filename === '') return 'index';
        
        return 'generic';
    }

    /**
     * Fix home page specific issues
     */
    fixHomePage() {
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            heroSection.style.paddingTop = '2rem';
            
            const minVhElements = heroSection.querySelectorAll('.min-vh-100');
            minVhElements.forEach(element => {
                element.style.minHeight = `calc(100vh - ${this.navHeight}px)`;
            });
        }
    }

    /**
     * Fix dashboard page specific issues
     */
    fixDashboardPage() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.style.position = 'relative';
            sidebar.style.zIndex = '1';
        }

        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.style.paddingTop = '1rem';
        }
    }

    /**
     * Fix interactive book page specific issues
     */
    fixInteractiveBookPage() {
        const containers = document.querySelectorAll('.container, .mx-auto');
        containers.forEach(container => {
            container.style.position = 'relative';
            container.style.zIndex = '1';
        });
    }

    /**
     * Fix showcase page specific issues
     */
    fixShowcasePage() {
        const showcaseHeader = document.querySelector('.showcase-header');
        if (showcaseHeader) {
            showcaseHeader.style.marginTop = '0';
            showcaseHeader.style.paddingTop = '3rem';
        }
    }

    /**
     * Fix generic page issues
     */
    fixGenericPage() {
        const mainElements = document.querySelectorAll('main, .main-content, section');
        mainElements.forEach(element => {
            element.style.position = 'relative';
            element.style.zIndex = '1';
        });
    }

    /**
     * Public method to manually trigger fixes
     */
    static applyFixes() {
        const manager = new LayoutManager();
        manager.runAllFixes();
        manager.fixPageSpecific();
    }
}

// Initialize layout manager
let layoutManager;

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        layoutManager = new LayoutManager();
    });
} else {
    layoutManager = new LayoutManager();
}

// Make it globally available
window.LayoutManager = LayoutManager;
window.layoutManager = layoutManager;

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LayoutManager;
}
