<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n-key="advanced_demo_title">Advanced Bilingual Features Demo</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
    
    <style>
        .demo-section {
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: transform 0.2s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 3rem;
        }
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .voice-controls {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .analytics-widget {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 1.5rem;
        }
    </style>
</head>
<body data-stage-key="advanced_demo">
    <!-- Demo Header -->
    <div class="demo-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-rocket me-3"></i><span data-i18n-key="advanced_demo_title">Advanced Bilingual Features Demo</span></h1>
                    <p class="lead mb-0" data-i18n-key="demo_subtitle">Experience the full power of our bilingual medical device innovation platform</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <button class="btn btn-outline-light btn-lg" id="languageToggle" data-i18n-key="language_toggle" title="Switch language">
                        <i class="fas fa-globe me-2"></i>
                        <span class="current-lang">EN</span>
                        <span class="separator">|</span>
                        <span class="other-lang">ع</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" data-i18n-number="500000000" data-i18n-currency="500000000">$500M</div>
                    <div class="metric-label" data-i18n-key="global_market">Global Market</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" data-i18n-percentage="25">25%</div>
                    <div class="metric-label" data-i18n-key="growth_rate">Annual Growth</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" data-i18n-number="20">20</div>
                    <div class="metric-label" data-i18n-key="total_stages">Learning Stages</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" data-i18n-date="2024-01-01">2024</div>
                    <div class="metric-label" data-i18n-key="last_updated">Last Updated</div>
                </div>
            </div>
        </div>

        <!-- Voice Narration Demo -->
        <div class="demo-section">
            <h2><i class="fas fa-volume-up me-3"></i><span data-i18n-key="voice_narration">Voice Narration</span></h2>
            <p data-i18n-key="voice_demo_desc">Experience our advanced text-to-speech system with support for both English and Arabic.</p>
            
            <div class="voice-controls">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="demo-text" id="voiceDemoText">
                            <p data-i18n-key="voice_demo_text">Medical device innovation requires a systematic approach to transform healthcare challenges into breakthrough solutions. Our comprehensive guide provides the framework and tools needed for successful innovation.</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="demoVoiceNarration()">
                                <i class="fas fa-play me-2"></i><span data-i18n-key="play_narration">Play Narration</span>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="stopVoiceNarration()">
                                <i class="fas fa-stop me-2"></i><span data-i18n-key="stop_narration">Stop</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced i18n Features -->
        <div class="demo-section">
            <h2><i class="fas fa-language me-3"></i><span data-i18n-key="advanced_i18n">Advanced Internationalization</span></h2>
            <p data-i18n-key="i18n_demo_desc">See how our system handles numbers, dates, currencies, and cultural adaptations.</p>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-dollar-sign me-2"></i><span data-i18n-key="currency_formatting">Currency Formatting</span></h5>
                        <div class="demo-examples">
                            <div class="d-flex justify-content-between">
                                <span>Market Size:</span>
                                <strong data-i18n-currency="1500000000">$1.5B</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Investment:</span>
                                <strong data-i18n-currency="50000000">$50M</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Revenue:</span>
                                <strong data-i18n-currency="250000000">$250M</strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-calendar me-2"></i><span data-i18n-key="date_formatting">Date Formatting</span></h5>
                        <div class="demo-examples">
                            <div class="d-flex justify-content-between">
                                <span data-i18n-key="project_start">Project Start:</span>
                                <strong data-i18n-date="2024-01-15">Jan 15, 2024</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span data-i18n-key="milestone_date">Milestone:</span>
                                <strong data-i18n-date="2024-06-30">Jun 30, 2024</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span data-i18n-key="launch_date">Launch:</span>
                                <strong data-i18n-date="2024-12-01">Dec 1, 2024</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-percentage me-2"></i><span data-i18n-key="percentage_formatting">Percentage Formatting</span></h5>
                        <div class="demo-examples">
                            <div class="d-flex justify-content-between">
                                <span data-i18n-key="success_rate">Success Rate:</span>
                                <strong data-i18n-percentage="85">85%</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span data-i18n-key="completion_rate">Completion:</span>
                                <strong data-i18n-percentage="67">67%</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span data-i18n-key="growth_rate">Growth:</span>
                                <strong data-i18n-percentage="23">23%</strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-clock me-2"></i><span data-i18n-key="time_formatting">Time Formatting</span></h5>
                        <div class="demo-examples">
                            <div class="d-flex justify-content-between">
                                <span data-i18n-key="last_activity">Last Activity:</span>
                                <strong data-i18n-relative-time="2024-01-01T10:00:00Z">2 hours ago</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span data-i18n-key="next_review">Next Review:</span>
                                <strong data-i18n-relative-time="2024-12-31T15:30:00Z">in 5 days</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Management Demo -->
        <div class="demo-section">
            <h2><i class="fas fa-edit me-3"></i><span data-i18n-key="content_management">Content Management</span></h2>
            <p data-i18n-key="content_demo_desc">Manage bilingual content with our advanced content management system.</p>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="feature-card">
                        <h5 data-i18n-key="dynamic_content">Dynamic Content Rendering</h5>
                        <div class="accordion" id="contentDemo">
                            <!-- Content will be loaded dynamically -->
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="loadDemoContent()">
                                <i class="fas fa-sync me-2"></i><span data-i18n-key="load_content">Load Content</span>
                            </button>
                            <a href="content-manager.html" class="btn btn-outline-primary ms-2">
                                <i class="fas fa-cogs me-2"></i><span data-i18n-key="open_cms">Open CMS</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="analytics-widget">
                        <h6><i class="fas fa-chart-line me-2"></i><span data-i18n-key="analytics">Analytics</span></h6>
                        <div id="analyticsPreview">
                            <div class="mb-2">
                                <small data-i18n-key="language_usage">Language Usage:</small>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-light" style="width: 60%"></div>
                                </div>
                                <small>English: 60% | Arabic: 40%</small>
                            </div>
                            <div class="mb-2">
                                <small data-i18n-key="page_views">Page Views:</small>
                                <div><strong data-i18n-number="1247">1,247</strong></div>
                            </div>
                            <div class="mb-2">
                                <small data-i18n-key="avg_session">Avg Session:</small>
                                <div><strong>8m 32s</strong></div>
                            </div>
                        </div>
                        <button class="btn btn-light btn-sm mt-2" onclick="showAnalyticsDashboard()">
                            <i class="fas fa-external-link-alt me-1"></i><span data-i18n-key="view_details">View Details</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Features -->
        <div class="demo-section">
            <h2><i class="fas fa-tachometer-alt me-3"></i><span data-i18n-key="performance">Performance Features</span></h2>
            <p data-i18n-key="performance_demo_desc">Experience our optimized loading and caching systems.</p>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-rocket fa-3x text-primary mb-3"></i>
                        <h6 data-i18n-key="lazy_loading">Lazy Loading</h6>
                        <p class="small text-muted" data-i18n-key="lazy_loading_desc">Content loads on demand for faster performance</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-database fa-3x text-success mb-3"></i>
                        <h6 data-i18n-key="smart_caching">Smart Caching</h6>
                        <p class="small text-muted" data-i18n-key="caching_desc">Intelligent caching reduces load times</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                        <h6 data-i18n-key="real_time_analytics">Real-time Analytics</h6>
                        <p class="small text-muted" data-i18n-key="analytics_desc">Monitor usage and performance in real-time</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="demo-section">
            <h2><i class="fas fa-link me-3"></i><span data-i18n-key="explore_more">Explore More</span></h2>
            <div class="row">
                <div class="col-md-3">
                    <a href="index.html" class="btn btn-outline-primary w-100 mb-2">
                        <i class="fas fa-home me-2"></i><span data-i18n-key="nav_home">Home</span>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="dashboard.html" class="btn btn-outline-success w-100 mb-2">
                        <i class="fas fa-tachometer-alt me-2"></i><span data-i18n-key="nav_dashboard">Dashboard</span>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="stage-01-bilingual.html" class="btn btn-outline-info w-100 mb-2">
                        <i class="fas fa-play me-2"></i><span data-i18n-key="start_learning">Start Learning</span>
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="test-bilingual.html" class="btn btn-outline-warning w-100 mb-2">
                        <i class="fas fa-flask me-2"></i><span data-i18n-key="test_features">Test Features</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/i18n.js"></script>
    <script src="js/i18n-advanced.js"></script>
    <script src="js/content-store.js"></script>
    <script src="js/ui-builder.js"></script>
    <script src="js/voice-narration.js"></script>
    <script src="js/analytics-performance.js"></script>
    <script src="main.js"></script>
    
    <script>
        // Demo-specific functions
        function demoVoiceNarration() {
            const textElement = document.getElementById('voiceDemoText');
            if (window.voiceNarration) {
                window.voiceNarration.speakElement(textElement);
            } else {
                alert('Voice narration system not available');
            }
        }
        
        function stopVoiceNarration() {
            if (window.voiceNarration) {
                window.voiceNarration.stop();
            }
        }
        
        function loadDemoContent() {
            const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
            if (window.UIBuilder) {
                window.UIBuilder.renderGuideContent('stage_01_introduction', currentLang, 'contentDemo');
            }
        }
        
        function showAnalyticsDashboard() {
            if (window.showAnalyticsDashboard) {
                window.showAnalyticsDashboard();
            } else {
                alert('Analytics dashboard will open here');
            }
        }
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            // Load initial content
            setTimeout(() => {
                loadDemoContent();
            }, 1000);
            
            // Update advanced elements when language changes
            window.addEventListener('languageChanged', function(event) {
                setTimeout(() => {
                    if (window.AdvancedI18n) {
                        window.AdvancedI18n.updateAdvancedElements(event.detail.language);
                    }
                }, 100);
            });
            
            // Initial update of advanced elements
            setTimeout(() => {
                if (window.AdvancedI18n) {
                    const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
                    window.AdvancedI18n.updateAdvancedElements(currentLang);
                }
            }, 500);
        });
    </script>
</body>
</html>
