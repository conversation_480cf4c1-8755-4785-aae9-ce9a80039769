/**
 * Medical Device Innovation Guide - Workspace Tools
 * JavaScript functions and data structures for interactive workspace tools
 */

// Global project data structure
let projectData = {
    metadata: {
        projectName: '',
        createdDate: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        currentStage: 1,
        completedStages: []
    },
    
    // Stage 3: Innovation Process
    stage3: {
        processMap: {
            innovationType: '',
            riskClass: '',
            targetMarket: '',
            timeline: '',
            generatedSteps: []
        },
        stageGates: {
            discovery: { status: 'not-started', criteria: [], notes: '' },
            concept: { status: 'not-started', criteria: [], notes: '' },
            development: { status: 'not-started', criteria: [], notes: '' },
            testing: { status: 'not-started', criteria: [], notes: '' },
            launch: { status: 'not-started', criteria: [], notes: '' }
        }
    },
    
    // Stage 4: Understanding Opportunity
    stage4: {
        opportunityAssessment: {
            marketSize: 3,
            growthRate: 3,
            unmetNeed: 3,
            techFeasibility: 3,
            devRisk: 3,
            ipPotential: 3,
            revenuePotential: 3,
            compAdvantage: 3,
            regPathway: 3,
            totalScore: 27,
            interpretation: ''
        },
        swotAnalysis: {
            strengths: '',
            weaknesses: '',
            opportunities: '',
            threats: ''
        }
    },
    
    // Stage 5: Needs Discovery
    stage5: {
        stakeholderMap: {
            primaryUsers: [],
            secondaryUsers: [],
            decisionMakers: [],
            influencers: []
        },
        interviewPlans: [],
        needsPrioritization: {
            highHigh: [],
            highLow: [],
            lowHigh: [],
            lowLow: []
        }
    },
    
    // Stage 6: Idea Generation
    stage6: {
        brainstormingSessions: [],
        concepts: [],
        conceptEvaluations: []
    },
    
    // Stage 7: Testing Your Idea
    stage7: {
        prototypePlans: [],
        testProtocols: [],
        testResults: []
    },
    
    // Stage 8: Clinical Trials
    stage8: {
        studyProtocols: [],
        regulatoryPlans: [],
        recruitmentPlans: []
    },
    
    // Stage 9: Reliability Considerations
    stage9: {
        fmeaEntries: [],
        riskControls: {
            inherentSafety: [],
            protectiveMeasures: [],
            informationSafety: []
        },
        reliabilityTests: []
    }
};

// Utility functions
function saveProjectData() {
    projectData.metadata.lastModified = new Date().toISOString();
    localStorage.setItem('medDevice_projectData', JSON.stringify(projectData));
}

function loadProjectData() {
    const stored = localStorage.getItem('medDevice_projectData');
    if (stored) {
        projectData = { ...projectData, ...JSON.parse(stored) };
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Stage 3: Innovation Process Functions
function generateProcessMap(stageNumber) {
    const innovationType = document.getElementById('innovationType').value;
    const riskClass = document.getElementById('riskClass').value;
    const targetMarket = document.getElementById('targetMarket').value;
    const timeline = document.getElementById('timeline').value;
    
    if (!innovationType || !riskClass || !targetMarket || !timeline) {
        showNotification('Please fill in all fields to generate process map', 'error');
        return;
    }
    
    // Generate process steps based on selections
    const processSteps = generateProcessSteps(innovationType, riskClass, targetMarket, timeline);
    
    // Save to project data
    projectData.stage3.processMap = {
        innovationType,
        riskClass,
        targetMarket,
        timeline,
        generatedSteps: processSteps
    };
    
    // Display process map
    displayProcessMap(processSteps);
    
    saveProjectData();
    showNotification('Process map generated successfully!', 'success');
}

function generateProcessSteps(type, risk, market, timeline) {
    const baseSteps = [
        { name: 'Needs Assessment', duration: '2-4 weeks', description: 'Identify and validate clinical needs' },
        { name: 'Concept Development', duration: '4-8 weeks', description: 'Develop and refine device concept' },
        { name: 'Feasibility Study', duration: '8-12 weeks', description: 'Assess technical and commercial feasibility' },
        { name: 'Prototype Development', duration: '12-24 weeks', description: 'Build and test initial prototypes' },
        { name: 'Regulatory Planning', duration: '4-8 weeks', description: 'Develop regulatory strategy' },
        { name: 'Clinical Testing', duration: '24-52 weeks', description: 'Conduct clinical validation studies' },
        { name: 'Manufacturing Setup', duration: '16-32 weeks', description: 'Establish manufacturing processes' },
        { name: 'Market Launch', duration: '8-16 weeks', description: 'Launch product to market' }
    ];
    
    // Adjust steps based on selections
    if (risk === 'class3') {
        baseSteps.splice(5, 0, { 
            name: 'Pre-Clinical Testing', 
            duration: '16-24 weeks', 
            description: 'Extensive pre-clinical validation' 
        });
    }
    
    if (timeline === 'fast') {
        baseSteps.forEach(step => {
            step.duration = step.duration.replace(/(\d+)-(\d+)/, (match, min, max) => {
                return `${Math.ceil(min * 0.7)}-${Math.ceil(max * 0.7)}`;
            });
        });
    }
    
    return baseSteps;
}

function displayProcessMap(steps) {
    const container = document.getElementById('processSteps');
    let html = '<div class="process-steps-visualization">';
    
    steps.forEach((step, index) => {
        html += `
            <div class="process-step-item">
                <div class="step-number">${index + 1}</div>
                <div class="step-content">
                    <h6>${step.name}</h6>
                    <p class="step-duration">${step.duration}</p>
                    <p class="step-description">${step.description}</p>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
    document.getElementById('processMapResult').style.display = 'block';
}

// Stage 4: Opportunity Assessment Functions
function updateOpportunityScore() {
    const factors = [
        'marketSize', 'growthRate', 'unmetNeed', 'techFeasibility', 
        'devRisk', 'ipPotential', 'revenuePotential', 'compAdvantage', 'regPathway'
    ];
    
    let totalScore = 0;
    factors.forEach(factor => {
        const value = parseInt(document.getElementById(factor).value);
        totalScore += value;
        
        // Update rating display
        const ratingElement = document.getElementById(factor).parentNode.querySelector('.rating-value');
        if (ratingElement) {
            ratingElement.textContent = value;
        }
    });
    
    // Update display
    document.getElementById('totalScore').textContent = `${totalScore}/45`;
    const percentage = (totalScore / 45) * 100;
    document.getElementById('scoreProgress').style.width = `${percentage}%`;
    
    // Update interpretation
    let interpretation = '';
    if (totalScore >= 36) {
        interpretation = 'Excellent opportunity with high potential';
    } else if (totalScore >= 27) {
        interpretation = 'Good opportunity with moderate potential';
    } else if (totalScore >= 18) {
        interpretation = 'Moderate opportunity with potential for improvement';
    } else {
        interpretation = 'Low opportunity - consider pivoting or major improvements';
    }
    
    document.getElementById('scoreInterpretation').innerHTML = `<small class="text-muted">${interpretation}</small>`;
    
    // Save to project data
    projectData.stage4.opportunityAssessment = {
        marketSize: parseInt(document.getElementById('marketSize').value),
        growthRate: parseInt(document.getElementById('growthRate').value),
        unmetNeed: parseInt(document.getElementById('unmetNeed').value),
        techFeasibility: parseInt(document.getElementById('techFeasibility').value),
        devRisk: parseInt(document.getElementById('devRisk').value),
        ipPotential: parseInt(document.getElementById('ipPotential').value),
        revenuePotential: parseInt(document.getElementById('revenuePotential').value),
        compAdvantage: parseInt(document.getElementById('compAdvantage').value),
        regPathway: parseInt(document.getElementById('regPathway').value),
        totalScore: totalScore,
        interpretation: interpretation
    };
}

function saveOpportunityAssessment(stageNumber) {
    updateOpportunityScore();
    saveProjectData();
    showNotification('Opportunity assessment saved successfully!', 'success');
}

function saveSWOTAnalysis(stageNumber) {
    projectData.stage4.swotAnalysis = {
        strengths: document.getElementById('swotStrengths').value,
        weaknesses: document.getElementById('swotWeaknesses').value,
        opportunities: document.getElementById('swotOpportunities').value,
        threats: document.getElementById('swotThreats').value
    };
    
    saveProjectData();
    showNotification('SWOT analysis saved successfully!', 'success');
}

// Stage 5: Needs Discovery Functions
function addStakeholder(categoryId) {
    const container = document.getElementById(categoryId);
    const newItem = document.createElement('div');
    newItem.className = 'stakeholder-item';
    newItem.innerHTML = `
        <input type="text" class="form-control mb-2" placeholder="Enter stakeholder...">
        <button class="btn btn-sm btn-outline-danger" onclick="removeStakeholder(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newItem);
}

function removeStakeholder(button) {
    button.parentNode.remove();
}

function saveStakeholderMap(stageNumber) {
    const categories = ['primaryUsers', 'secondaryUsers', 'decisionMakers', 'influencers'];
    const stakeholderMap = {};
    
    categories.forEach(category => {
        const inputs = document.querySelectorAll(`#${category} input[type="text"]`);
        stakeholderMap[category] = Array.from(inputs)
            .map(input => input.value.trim())
            .filter(value => value !== '');
    });
    
    projectData.stage5.stakeholderMap = stakeholderMap;
    saveProjectData();
    showNotification('Stakeholder map saved successfully!', 'success');
}

function addQuestion() {
    const container = document.getElementById('questionsList');
    const newQuestion = document.createElement('div');
    newQuestion.className = 'question-item mb-2';
    newQuestion.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" placeholder="Enter your question...">
            <button class="btn btn-outline-danger" onclick="removeQuestion(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    container.appendChild(newQuestion);
}

function removeQuestion(button) {
    button.closest('.question-item').remove();
}

function generateInterviewGuide(stageNumber) {
    const interviewType = document.getElementById('interviewType').value;
    const targetStakeholder = document.getElementById('targetStakeholder').value;
    const questions = Array.from(document.querySelectorAll('#questionsList input'))
        .map(input => input.value.trim())
        .filter(value => value !== '');
    
    if (!interviewType || !targetStakeholder || questions.length === 0) {
        showNotification('Please fill in all fields to generate interview guide', 'error');
        return;
    }
    
    const interviewPlan = {
        id: Date.now(),
        type: interviewType,
        stakeholder: targetStakeholder,
        questions: questions,
        createdDate: new Date().toISOString()
    };
    
    projectData.stage5.interviewPlans.push(interviewPlan);
    saveProjectData();
    showNotification('Interview guide generated and saved!', 'success');
}

function addNeedToMatrix() {
    const needDescription = document.getElementById('needDescription').value.trim();
    if (!needDescription) {
        showNotification('Please enter a need description', 'error');
        return;
    }
    
    // For demo purposes, randomly assign to a quadrant
    // In a real implementation, this would be based on user input for impact/frequency
    const quadrants = ['highHigh', 'highLow', 'lowHigh', 'lowLow'];
    const randomQuadrant = quadrants[Math.floor(Math.random() * quadrants.length)];
    
    const needItem = document.createElement('div');
    needItem.className = 'need-item';
    needItem.innerHTML = `
        <span>${needDescription}</span>
        <button class="btn btn-sm btn-outline-danger ms-2" onclick="removeNeed(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.getElementById(randomQuadrant).appendChild(needItem);
    document.getElementById('needDescription').value = '';
    
    // Save to project data
    if (!projectData.stage5.needsPrioritization[randomQuadrant]) {
        projectData.stage5.needsPrioritization[randomQuadrant] = [];
    }
    projectData.stage5.needsPrioritization[randomQuadrant].push(needDescription);
}

function removeNeed(button) {
    button.parentNode.remove();
}

function saveNeedsPrioritization(stageNumber) {
    // Update project data from current DOM state
    const quadrants = ['highHigh', 'highLow', 'lowHigh', 'lowLow'];
    quadrants.forEach(quadrant => {
        const needs = Array.from(document.querySelectorAll(`#${quadrant} .need-item span`))
            .map(span => span.textContent);
        projectData.stage5.needsPrioritization[quadrant] = needs;
    });
    
    saveProjectData();
    showNotification('Needs prioritization saved successfully!', 'success');
}

// Initialize project data on page load
document.addEventListener('DOMContentLoaded', function() {
    loadProjectData();
    
    // Set up event listeners for opportunity assessment sliders
    const assessmentSliders = document.querySelectorAll('#stage-04-workspace input[type="range"]');
    assessmentSliders.forEach(slider => {
        slider.addEventListener('input', updateOpportunityScore);
    });
});

// Stage 6: Idea Generation Functions
let brainstormingTimer = null;
let brainstormingActive = false;

function startBrainstormingSession(stageNumber) {
    const problemStatement = document.getElementById('problemStatement').value.trim();
    const method = document.getElementById('brainstormingMethod').value;
    const duration = parseInt(document.getElementById('sessionDuration').value);

    if (!problemStatement || !method) {
        showNotification('Please fill in problem statement and select method', 'error');
        return;
    }

    // Initialize session
    brainstormingActive = true;
    document.getElementById('brainstormingArea').style.display = 'block';
    document.getElementById('ideaCount').textContent = '0';
    document.getElementById('ideasContainer').innerHTML = '';

    // Start timer
    let timeLeft = duration * 60; // Convert to seconds
    updateTimer(timeLeft, duration * 60);

    brainstormingTimer = setInterval(() => {
        timeLeft--;
        updateTimer(timeLeft, duration * 60);

        if (timeLeft <= 0) {
            endBrainstormingSession(stageNumber);
        }
    }, 1000);

    // Focus on idea input
    document.getElementById('newIdea').focus();

    showNotification(`Brainstorming session started! Duration: ${duration} minutes`, 'success');
}

function updateTimer(timeLeft, totalTime) {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    document.getElementById('timeRemaining').textContent =
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    const progress = (timeLeft / totalTime) * 100;
    document.getElementById('timerProgress').style.width = `${progress}%`;
}

function handleIdeaInput(event) {
    if (event.key === 'Enter') {
        addIdea();
    }
}

function addIdea() {
    const ideaInput = document.getElementById('newIdea');
    const idea = ideaInput.value.trim();

    if (!idea) return;

    const ideaElement = document.createElement('div');
    ideaElement.className = 'idea-item';
    ideaElement.innerHTML = `
        <span>${idea}</span>
        <button class="btn btn-sm btn-outline-danger ms-2" onclick="removeIdea(this)">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.getElementById('ideasContainer').appendChild(ideaElement);

    // Update count
    const currentCount = parseInt(document.getElementById('ideaCount').textContent);
    document.getElementById('ideaCount').textContent = currentCount + 1;

    // Clear input and focus
    ideaInput.value = '';
    ideaInput.focus();

    // Update select options for concept development
    updateIdeaSelects();
}

function removeIdea(button) {
    button.parentNode.remove();
    const currentCount = parseInt(document.getElementById('ideaCount').textContent);
    document.getElementById('ideaCount').textContent = Math.max(0, currentCount - 1);
    updateIdeaSelects();
}

function endBrainstormingSession(stageNumber) {
    if (brainstormingTimer) {
        clearInterval(brainstormingTimer);
        brainstormingTimer = null;
    }

    brainstormingActive = false;

    // Save session data
    const ideas = Array.from(document.querySelectorAll('#ideasContainer .idea-item span'))
        .map(span => span.textContent);

    const session = {
        id: Date.now(),
        problemStatement: document.getElementById('problemStatement').value,
        method: document.getElementById('brainstormingMethod').value,
        duration: document.getElementById('sessionDuration').value,
        ideas: ideas,
        createdDate: new Date().toISOString()
    };

    projectData.stage6.brainstormingSessions.push(session);
    saveProjectData();

    showNotification(`Brainstorming session completed! Generated ${ideas.length} ideas`, 'success');
}

function updateIdeaSelects() {
    const ideas = Array.from(document.querySelectorAll('#ideasContainer .idea-item span'))
        .map(span => span.textContent);

    const selects = ['selectedIdea', 'conceptToEvaluate'];
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Clear existing options except first
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            // Add idea options
            ideas.forEach(idea => {
                const option = document.createElement('option');
                option.value = idea;
                option.textContent = idea.length > 50 ? idea.substring(0, 50) + '...' : idea;
                select.appendChild(option);
            });
        }
    });
}

function addFeature() {
    const container = document.getElementById('featuresList');
    const newFeature = document.createElement('div');
    newFeature.className = 'feature-item mb-2';
    newFeature.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" placeholder="Enter a key feature...">
            <button class="btn btn-outline-danger" onclick="removeFeature(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    container.appendChild(newFeature);
}

function removeFeature(button) {
    button.closest('.feature-item').remove();
}

function saveConcept(stageNumber) {
    const selectedIdea = document.getElementById('selectedIdea').value;
    const conceptName = document.getElementById('conceptName').value.trim();
    const conceptDescription = document.getElementById('conceptDescription').value.trim();
    const targetUsers = document.getElementById('targetUsers').value.trim();
    const useCase = document.getElementById('useCase').value.trim();

    if (!selectedIdea || !conceptName || !conceptDescription) {
        showNotification('Please fill in required fields', 'error');
        return;
    }

    const features = Array.from(document.querySelectorAll('#featuresList input'))
        .map(input => input.value.trim())
        .filter(value => value !== '');

    const concept = {
        id: Date.now(),
        baseIdea: selectedIdea,
        name: conceptName,
        description: conceptDescription,
        features: features,
        targetUsers: targetUsers,
        useCase: useCase,
        createdDate: new Date().toISOString()
    };

    projectData.stage6.concepts.push(concept);
    saveProjectData();

    // Update concept evaluation select
    updateConceptEvaluationSelect();

    showNotification('Concept saved successfully!', 'success');
}

function updateConceptEvaluationSelect() {
    const select = document.getElementById('conceptToEvaluate');
    if (select) {
        // Clear existing options except first
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // Add concept options
        projectData.stage6.concepts.forEach(concept => {
            const option = document.createElement('option');
            option.value = concept.id;
            option.textContent = concept.name;
            select.appendChild(option);
        });
    }
}

function saveConceptEvaluation(stageNumber) {
    const conceptId = document.getElementById('conceptToEvaluate').value;
    if (!conceptId) {
        showNotification('Please select a concept to evaluate', 'error');
        return;
    }

    const criteria = ['evalTechFeasibility', 'evalMarketNeed', 'evalNovelty', 'evalCommercial', 'evalRisk'];
    const scores = {};
    let totalScore = 0;

    criteria.forEach(criterion => {
        const value = parseInt(document.getElementById(criterion).value);
        scores[criterion] = value;
        totalScore += value;
    });

    const evaluation = {
        id: Date.now(),
        conceptId: conceptId,
        scores: scores,
        totalScore: totalScore,
        maxScore: 25,
        percentage: (totalScore / 25) * 100,
        createdDate: new Date().toISOString()
    };

    projectData.stage6.conceptEvaluations.push(evaluation);
    saveProjectData();

    showNotification('Concept evaluation saved successfully!', 'success');
}

// Stage 7: Testing Your Idea Functions
function savePrototypePlan(stageNumber) {
    const prototypePlan = {
        id: Date.now(),
        type: document.getElementById('prototypeType').value,
        goal: document.getElementById('testingGoal').value,
        users: document.getElementById('testUsers').value,
        environment: document.getElementById('testEnvironment').value,
        successCriteria: document.getElementById('successCriteria').value,
        createdDate: new Date().toISOString()
    };

    projectData.stage7.prototypePlans.push(prototypePlan);
    saveProjectData();
    showNotification('Prototype plan saved successfully!', 'success');
}

function addTestTask() {
    const container = document.getElementById('testTasks');
    const newTask = document.createElement('div');
    newTask.className = 'task-item mb-2';
    newTask.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" placeholder="Describe a test task...">
            <button class="btn btn-outline-danger" onclick="removeTestTask(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    container.appendChild(newTask);
}

function removeTestTask(button) {
    button.closest('.task-item').remove();
}

function generateTestProtocol(stageNumber) {
    const testMethod = document.getElementById('testMethod').value;
    const participantCount = document.getElementById('participantCount').value;
    const testDuration = document.getElementById('testDuration').value;

    if (!testMethod) {
        showNotification('Please select a test method', 'error');
        return;
    }

    const tasks = Array.from(document.querySelectorAll('#testTasks input'))
        .map(input => input.value.trim())
        .filter(value => value !== '');

    const metrics = [];
    const metricCheckboxes = ['metricTime', 'metricSuccess', 'metricErrors', 'metricSatisfaction', 'metricLearnability'];
    metricCheckboxes.forEach(metricId => {
        if (document.getElementById(metricId).checked) {
            metrics.push(document.querySelector(`label[for="${metricId}"]`).textContent);
        }
    });

    const protocol = {
        id: Date.now(),
        method: testMethod,
        participantCount: participantCount,
        duration: testDuration,
        tasks: tasks,
        metrics: metrics,
        createdDate: new Date().toISOString()
    };

    projectData.stage7.testProtocols.push(protocol);
    saveProjectData();
    showNotification('Test protocol generated and saved!', 'success');
}

let currentTestSession = null;
let currentParticipantIndex = 0;

function startTestSession(stageNumber) {
    const sessionName = document.getElementById('sessionName').value.trim();
    if (!sessionName) {
        showNotification('Please enter a session name', 'error');
        return;
    }

    currentTestSession = {
        id: Date.now(),
        name: sessionName,
        participants: [],
        startTime: new Date().toISOString()
    };

    currentParticipantIndex = 0;

    document.getElementById('activeSession').style.display = 'block';
    document.getElementById('currentSession').textContent = sessionName;
    updateParticipantDisplay();

    showNotification('Test session started!', 'success');
}

function updateParticipantDisplay() {
    const totalParticipants = parseInt(document.getElementById('participantCount').value) || 5;
    document.getElementById('currentParticipant').textContent = currentParticipantIndex + 1;
    document.getElementById('totalParticipants').textContent = totalParticipants;
}

function recordParticipantData(stageNumber) {
    if (!currentTestSession) return;

    const participantData = {
        participantNumber: currentParticipantIndex + 1,
        completionTime: document.getElementById('completionTime').value,
        successRate: document.getElementById('successRate').value,
        observations: document.getElementById('observations').value,
        timestamp: new Date().toISOString()
    };

    currentTestSession.participants.push(participantData);

    // Clear form
    document.getElementById('completionTime').value = '';
    document.getElementById('successRate').value = '';
    document.getElementById('observations').value = '';

    showNotification('Participant data recorded!', 'success');
}

function nextParticipant(stageNumber) {
    currentParticipantIndex++;
    updateParticipantDisplay();
    showNotification('Ready for next participant', 'info');
}

function completeTestSession(stageNumber) {
    if (!currentTestSession) return;

    currentTestSession.endTime = new Date().toISOString();
    projectData.stage7.testResults.push(currentTestSession);
    saveProjectData();

    // Generate results summary
    generateResultsSummary();

    document.getElementById('activeSession').style.display = 'none';
    currentTestSession = null;
    currentParticipantIndex = 0;

    showNotification('Test session completed and saved!', 'success');
}

function generateResultsSummary() {
    if (!currentTestSession || currentTestSession.participants.length === 0) return;

    const participants = currentTestSession.participants;
    const avgCompletionTime = participants.reduce((sum, p) => sum + (parseFloat(p.completionTime) || 0), 0) / participants.length;
    const avgSuccessRate = participants.reduce((sum, p) => sum + (parseFloat(p.successRate) || 0), 0) / participants.length;

    const summaryHtml = `
        <div class="results-summary">
            <h6>${currentTestSession.name} Results</h6>
            <p><strong>Participants:</strong> ${participants.length}</p>
            <p><strong>Average Completion Time:</strong> ${avgCompletionTime.toFixed(1)} seconds</p>
            <p><strong>Average Success Rate:</strong> ${avgSuccessRate.toFixed(1)}%</p>
        </div>
    `;

    document.getElementById('resultsSummary').innerHTML = summaryHtml;
}

// Export additional functions for global use
window.startBrainstormingSession = startBrainstormingSession;
window.handleIdeaInput = handleIdeaInput;
window.addIdea = addIdea;
window.removeIdea = removeIdea;
window.endBrainstormingSession = endBrainstormingSession;
window.addFeature = addFeature;
window.removeFeature = removeFeature;
window.saveConcept = saveConcept;
window.saveConceptEvaluation = saveConceptEvaluation;
window.savePrototypePlan = savePrototypePlan;
window.addTestTask = addTestTask;
window.removeTestTask = removeTestTask;
window.generateTestProtocol = generateTestProtocol;
window.startTestSession = startTestSession;
window.recordParticipantData = recordParticipantData;
window.nextParticipant = nextParticipant;
window.completeTestSession = completeTestSession;

// Stage 8: Clinical Trials Functions
function addSecondaryEndpoint() {
    const container = document.getElementById('secondaryEndpoints');
    const newEndpoint = document.createElement('div');
    newEndpoint.className = 'endpoint-item mb-2';
    newEndpoint.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" placeholder="Secondary endpoint...">
            <button class="btn btn-outline-danger" onclick="removeEndpoint(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    container.appendChild(newEndpoint);
}

function removeEndpoint(button) {
    button.closest('.endpoint-item').remove();
}

function generateStudyProtocol(stageNumber) {
    const studyPhase = document.getElementById('studyPhase').value;
    const studyDesign = document.getElementById('studyDesign').value;
    const primaryEndpoint = document.getElementById('primaryEndpoint').value.trim();
    const sampleSize = document.getElementById('sampleSize').value;
    const studyDuration = document.getElementById('studyDuration').value;

    if (!studyPhase || !studyDesign || !primaryEndpoint || !sampleSize) {
        showNotification('Please fill in required fields', 'error');
        return;
    }

    const secondaryEndpoints = Array.from(document.querySelectorAll('#secondaryEndpoints input'))
        .map(input => input.value.trim())
        .filter(value => value !== '');

    const protocol = {
        id: Date.now(),
        phase: studyPhase,
        design: studyDesign,
        primaryEndpoint: primaryEndpoint,
        secondaryEndpoints: secondaryEndpoints,
        sampleSize: parseInt(sampleSize),
        duration: parseInt(studyDuration),
        inclusionCriteria: document.getElementById('inclusionCriteria').value,
        exclusionCriteria: document.getElementById('exclusionCriteria').value,
        createdDate: new Date().toISOString()
    };

    projectData.stage8.studyProtocols.push(protocol);
    saveProjectData();
    showNotification('Study protocol generated and saved!', 'success');
}

function generateRegulatoryPlan(stageNumber) {
    const market = document.getElementById('regulatoryMarket').value;
    const deviceClass = document.getElementById('deviceClass').value;
    const strategy = document.getElementById('regulatoryStrategy').value;

    if (!market || !deviceClass || !strategy) {
        showNotification('Please select all regulatory options', 'error');
        return;
    }

    // Generate timeline based on strategy
    const timeline = generateRegulatoryTimeline(strategy, deviceClass);
    displayRegulatoryTimeline(timeline);

    const regulatoryPlan = {
        id: Date.now(),
        market: market,
        deviceClass: deviceClass,
        strategy: strategy,
        timeline: timeline,
        createdDate: new Date().toISOString()
    };

    projectData.stage8.regulatoryPlans.push(regulatoryPlan);
    saveProjectData();
    showNotification('Regulatory plan generated and saved!', 'success');
}

function generateRegulatoryTimeline(strategy, deviceClass) {
    const timelines = {
        '510k': [
            { step: 'Pre-Submission Meeting', duration: '2-3 months', description: 'Optional FDA meeting' },
            { step: '510(k) Preparation', duration: '3-6 months', description: 'Document preparation' },
            { step: 'FDA Review', duration: '3-6 months', description: 'FDA review process' },
            { step: 'Response to Questions', duration: '1-3 months', description: 'Address FDA questions' },
            { step: 'Clearance', duration: '1 month', description: 'Final clearance' }
        ],
        'pma': [
            { step: 'Pre-IDE Meeting', duration: '3-6 months', description: 'FDA consultation' },
            { step: 'IDE Submission', duration: '6-12 months', description: 'Clinical trial approval' },
            { step: 'Clinical Studies', duration: '12-36 months', description: 'Clinical data collection' },
            { step: 'PMA Preparation', duration: '6-12 months', description: 'Application preparation' },
            { step: 'FDA Review', duration: '6-12 months', description: 'FDA review process' },
            { step: 'Panel Meeting', duration: '3-6 months', description: 'Advisory panel review' },
            { step: 'Approval', duration: '1-3 months', description: 'Final approval' }
        ],
        'de-novo': [
            { step: 'Pre-Submission', duration: '3-6 months', description: 'FDA consultation' },
            { step: 'De Novo Preparation', duration: '6-12 months', description: 'Application preparation' },
            { step: 'FDA Review', duration: '6-9 months', description: 'FDA review process' },
            { step: 'Classification', duration: '1-2 months', description: 'Device classification' }
        ]
    };

    return timelines[strategy] || [];
}

function displayRegulatoryTimeline(timeline) {
    let html = '<div class="timeline-visualization">';
    timeline.forEach((step, index) => {
        html += `
            <div class="timeline-step">
                <div class="step-number">${index + 1}</div>
                <div class="step-content">
                    <h6>${step.step}</h6>
                    <p class="step-duration">${step.duration}</p>
                    <p class="step-description">${step.description}</p>
                </div>
            </div>
        `;
    });
    html += '</div>';

    document.getElementById('timelineSteps').innerHTML = html;
}

function saveRecruitmentPlan(stageNumber) {
    const targetPopulation = document.getElementById('targetPopulation').value.trim();
    const recruitmentRate = document.getElementById('recruitmentRate').value;
    const dropoutRate = document.getElementById('dropoutRate').value;
    const retentionStrategies = document.getElementById('retentionStrategies').value.trim();

    const channels = [];
    const channelCheckboxes = ['channelClinics', 'channelReferrals', 'channelAdvertising', 'channelRegistries', 'channelSocial'];
    channelCheckboxes.forEach(channelId => {
        if (document.getElementById(channelId).checked) {
            channels.push(document.querySelector(`label[for="${channelId}"]`).textContent);
        }
    });

    const recruitmentPlan = {
        id: Date.now(),
        targetPopulation: targetPopulation,
        channels: channels,
        recruitmentRate: parseInt(recruitmentRate),
        dropoutRate: parseInt(dropoutRate),
        retentionStrategies: retentionStrategies,
        createdDate: new Date().toISOString()
    };

    projectData.stage8.recruitmentPlans.push(recruitmentPlan);
    saveProjectData();
    showNotification('Recruitment plan saved successfully!', 'success');
}

// Stage 9: Reliability Considerations Functions
function addFMEAEntry(stageNumber) {
    const component = document.getElementById('deviceComponent').value.trim();
    const failureMode = document.getElementById('failureMode').value.trim();
    const effects = document.getElementById('failureEffects').value.trim();
    const causes = document.getElementById('failureCauses').value.trim();
    const severity = parseInt(document.getElementById('severity').value);
    const occurrence = parseInt(document.getElementById('occurrence').value);
    const detection = parseInt(document.getElementById('detection').value);
    const actions = document.getElementById('recommendedActions').value.trim();

    if (!component || !failureMode || !effects || !severity || !occurrence || !detection) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    const rpn = severity * occurrence * detection;

    const fmeaEntry = {
        id: Date.now(),
        component: component,
        failureMode: failureMode,
        effects: effects,
        causes: causes,
        severity: severity,
        occurrence: occurrence,
        detection: detection,
        rpn: rpn,
        actions: actions,
        createdDate: new Date().toISOString()
    };

    projectData.stage9.fmeaEntries.push(fmeaEntry);
    updateFMEATable();
    clearFMEAForm();
    saveProjectData();
    showNotification('FMEA entry added successfully!', 'success');
}

function updateFMEATable() {
    const tbody = document.getElementById('fmeaTableBody');
    if (projectData.stage9.fmeaEntries.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No FMEA entries yet</td></tr>';
        return;
    }

    let html = '';
    projectData.stage9.fmeaEntries.forEach(entry => {
        const rpnClass = entry.rpn >= 200 ? 'text-danger' : entry.rpn >= 100 ? 'text-warning' : 'text-success';
        html += `
            <tr>
                <td>${entry.component}</td>
                <td>${entry.failureMode}</td>
                <td>${entry.effects.substring(0, 50)}${entry.effects.length > 50 ? '...' : ''}</td>
                <td>${entry.severity}</td>
                <td>${entry.occurrence}</td>
                <td>${entry.detection}</td>
                <td class="${rpnClass}"><strong>${entry.rpn}</strong></td>
                <td>${entry.actions.substring(0, 30)}${entry.actions.length > 30 ? '...' : ''}</td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

function clearFMEAForm() {
    document.getElementById('deviceComponent').value = '';
    document.getElementById('failureMode').value = '';
    document.getElementById('failureEffects').value = '';
    document.getElementById('failureCauses').value = '';
    document.getElementById('severity').value = '';
    document.getElementById('occurrence').value = '';
    document.getElementById('detection').value = '';
    document.getElementById('recommendedActions').value = '';
    document.getElementById('rpnValue').textContent = '-';
    document.getElementById('rpnProgress').style.width = '0%';
    document.getElementById('rpnInterpretation').textContent = 'Enter severity, occurrence, and detection values';
}

function addRiskMeasure(categoryId) {
    const container = document.getElementById(categoryId);
    const newMeasure = document.createElement('div');
    newMeasure.className = 'measure-item';
    newMeasure.innerHTML = `
        <input type="text" class="form-control mb-2" placeholder="Risk control measure...">
        <button class="btn btn-sm btn-outline-danger" onclick="removeMeasure(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newMeasure);
}

function removeMeasure(button) {
    button.parentNode.remove();
}

function saveRiskControls(stageNumber) {
    const categories = ['inherentSafety', 'protectiveMeasures', 'informationSafety'];
    const riskControls = {};

    categories.forEach(category => {
        const inputs = document.querySelectorAll(`#${category} input[type="text"]`);
        riskControls[category] = Array.from(inputs)
            .map(input => input.value.trim())
            .filter(value => value !== '');
    });

    projectData.stage9.riskControls = riskControls;
    saveProjectData();
    showNotification('Risk controls saved successfully!', 'success');
}

function addReliabilityTest(stageNumber) {
    const testType = document.getElementById('reliabilityTestType').value;
    const testStandard = document.getElementById('testStandard').value.trim();
    const sampleSize = document.getElementById('testSampleSize').value;
    const duration = document.getElementById('testDuration').value.trim();
    const conditions = document.getElementById('testConditions').value.trim();
    const criteria = document.getElementById('acceptanceCriteria').value.trim();

    if (!testType || !testStandard || !sampleSize) {
        showNotification('Please fill in required fields', 'error');
        return;
    }

    const reliabilityTest = {
        id: Date.now(),
        type: testType,
        standard: testStandard,
        sampleSize: parseInt(sampleSize),
        duration: duration,
        conditions: conditions,
        criteria: criteria,
        createdDate: new Date().toISOString()
    };

    projectData.stage9.reliabilityTests.push(reliabilityTest);
    updateTestingSchedule();
    clearReliabilityTestForm();
    saveProjectData();
    showNotification('Reliability test added to schedule!', 'success');
}

function updateTestingSchedule() {
    const container = document.getElementById('testingSchedule');
    if (projectData.stage9.reliabilityTests.length === 0) {
        container.innerHTML = '<p class="text-muted">No tests scheduled yet.</p>';
        return;
    }

    let html = '<div class="test-schedule-list">';
    projectData.stage9.reliabilityTests.forEach((test, index) => {
        html += `
            <div class="test-item">
                <h6>${test.type}</h6>
                <p><strong>Standard:</strong> ${test.standard}</p>
                <p><strong>Sample Size:</strong> ${test.sampleSize}</p>
                <p><strong>Duration:</strong> ${test.duration}</p>
            </div>
        `;
    });
    html += '</div>';

    container.innerHTML = html;
}

function clearReliabilityTestForm() {
    document.getElementById('reliabilityTestType').value = '';
    document.getElementById('testStandard').value = '';
    document.getElementById('testSampleSize').value = '';
    document.getElementById('testDuration').value = '';
    document.getElementById('testConditions').value = '';
    document.getElementById('acceptanceCriteria').value = '';
}

// RPN calculation for FMEA
document.addEventListener('DOMContentLoaded', function() {
    const fmeaInputs = ['severity', 'occurrence', 'detection'];
    fmeaInputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('change', updateRPN);
        }
    });
});

function updateRPN() {
    const severity = parseInt(document.getElementById('severity').value) || 0;
    const occurrence = parseInt(document.getElementById('occurrence').value) || 0;
    const detection = parseInt(document.getElementById('detection').value) || 0;

    if (severity && occurrence && detection) {
        const rpn = severity * occurrence * detection;
        document.getElementById('rpnValue').textContent = rpn;

        const percentage = (rpn / 1000) * 100; // Max RPN is 1000
        document.getElementById('rpnProgress').style.width = `${Math.min(percentage, 100)}%`;

        let interpretation = '';
        let progressClass = '';
        if (rpn >= 200) {
            interpretation = 'High risk - immediate action required';
            progressClass = 'bg-danger';
        } else if (rpn >= 100) {
            interpretation = 'Medium risk - action should be taken';
            progressClass = 'bg-warning';
        } else {
            interpretation = 'Low risk - monitor and review';
            progressClass = 'bg-success';
        }

        document.getElementById('rpnInterpretation').innerHTML = `<small class="text-muted">${interpretation}</small>`;
        document.getElementById('rpnProgress').className = `progress-bar ${progressClass}`;
    }
}

// Export Stage 8 & 9 functions for global use
window.addSecondaryEndpoint = addSecondaryEndpoint;
window.removeEndpoint = removeEndpoint;
window.generateStudyProtocol = generateStudyProtocol;
window.generateRegulatoryPlan = generateRegulatoryPlan;
window.saveRecruitmentPlan = saveRecruitmentPlan;
window.addFMEAEntry = addFMEAEntry;
window.addRiskMeasure = addRiskMeasure;
window.removeMeasure = removeMeasure;
window.saveRiskControls = saveRiskControls;
window.addReliabilityTest = addReliabilityTest;

// Export original functions for global use
window.generateProcessMap = generateProcessMap;
window.updateOpportunityScore = updateOpportunityScore;
window.saveOpportunityAssessment = saveOpportunityAssessment;
window.saveSWOTAnalysis = saveSWOTAnalysis;
window.addStakeholder = addStakeholder;
window.removeStakeholder = removeStakeholder;
window.saveStakeholderMap = saveStakeholderMap;
window.addQuestion = addQuestion;
window.removeQuestion = removeQuestion;
window.generateInterviewGuide = generateInterviewGuide;
window.addNeedToMatrix = addNeedToMatrix;
window.removeNeed = removeNeed;
window.saveNeedsPrioritization = saveNeedsPrioritization;
