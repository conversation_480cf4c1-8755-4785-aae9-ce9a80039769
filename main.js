/**
 * Medical Device Innovation Guide - Main JavaScript
 * Interactive functionality for the academic book platform
 */

// Global application state
const AppState = {
    currentUser: null,
    progress: {
        completedStages: [],
        timeSpent: 0,
        achievements: [],
        projectsStarted: 0
    },
    settings: {
        theme: 'light',
        language: 'en'
    }
};

// Stage data configuration
const STAGES_DATA = [
    { id: 1, title: "General Introduction", category: "Foundation", description: "Why this book? Understanding the foundation of medical device innovation" },
    { id: 2, title: "Medical Device Landscape", category: "Foundation", description: "Understanding the current state and future opportunities" },
    { id: 3, title: "Innovation Process", category: "Foundation", description: "Understanding the medical device development process" },
    { id: 4, title: "Understanding Opportunity", category: "Foundation", description: "Where to start? Identifying market opportunities" },
    { id: 5, title: "Needs Discovery", category: "Foundation", description: "Discovering and validating clinical needs" },
    { id: 6, title: "Idea Generation", category: "Development", description: "From problem to solution - generating innovative ideas" },
    { id: 7, title: "Testing Your Idea", category: "Development", description: "Validation and proof of concept development" },
    { id: 8, title: "Clinical Trials", category: "Development", description: "Proving value in the real world" },
    { id: 9, title: "Reliability Considerations", category: "Development", description: "Ensuring device safety and reliability" },
    { id: 10, title: "Innovation Notebooks", category: "Development", description: "Documenting your innovation journey" },
    { id: 11, title: "Patent Fundamentals", category: "Protection", description: "Protecting your most valuable assets" },
    { id: 12, title: "Regulatory Strategy", category: "Protection", description: "Navigating regulatory requirements" },
    { id: 13, title: "Reimbursement Fundamentals", category: "Protection", description: "Who will pay for your innovation?" },
    { id: 14, title: "Funding Your Idea", category: "Protection", description: "Securing financial resources for development" },
    { id: 15, title: "Business Plans", category: "Protection", description: "Creating a roadmap for success" },
    { id: 16, title: "Promoting Your Idea", category: "Market", description: "How to tell a compelling story" },
    { id: 17, title: "Resources", category: "Market", description: "Tools and resources for continued learning" },
    { id: 18, title: "Risk Management", category: "Market", description: "Managing risks throughout the lifecycle" },
    { id: 19, title: "Manufacturing & Supply Chain", category: "Market", description: "Scaling production and distribution" },
    { id: 20, title: "Post-Market Monitoring", category: "Market", description: "Continuous improvement and monitoring" }
];

// Local Storage Management
const Storage = {
    save: function(key, data) {
        try {
            localStorage.setItem(`medDevice_${key}`, JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save to localStorage:', error);
        }
    },
    
    load: function(key) {
        try {
            const data = localStorage.getItem(`medDevice_${key}`);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.warn('Failed to load from localStorage:', error);
            return null;
        }
    },
    
    remove: function(key) {
        try {
            localStorage.removeItem(`medDevice_${key}`);
        } catch (error) {
            console.warn('Failed to remove from localStorage:', error);
        }
    }
};

// Progress Management
const ProgressManager = {
    init: function() {
        const savedProgress = Storage.load('progress');
        if (savedProgress) {
            AppState.progress = { ...AppState.progress, ...savedProgress };
        }
        this.updateUI();
    },
    
    markStageAsVisited: function(stageId) {
        if (!AppState.progress.completedStages.includes(stageId)) {
            AppState.progress.completedStages.push(stageId);
            this.addAchievement(`Completed Stage ${stageId}`);
            this.save();
            this.updateUI();
        }
    },
    
    addTimeSpent: function(minutes) {
        AppState.progress.timeSpent += minutes;
        this.save();
        this.updateUI();
    },
    
    addAchievement: function(achievement) {
        const timestamp = new Date().toISOString();
        AppState.progress.achievements.push({
            title: achievement,
            timestamp: timestamp
        });
        this.addActivity(achievement, 'achievement');
    },
    
    incrementProjectsStarted: function() {
        AppState.progress.projectsStarted++;
        this.save();
        this.updateUI();
    },
    
    getCompletionPercentage: function() {
        return Math.round((AppState.progress.completedStages.length / STAGES_DATA.length) * 100);
    },
    
    save: function() {
        Storage.save('progress', AppState.progress);
    },
    
    updateUI: function() {
        // Update progress indicators
        const progressElements = document.querySelectorAll('.progress-percentage');
        progressElements.forEach(el => {
            el.textContent = this.getCompletionPercentage() + '%';
        });
        
        // Update progress bars
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            bar.style.width = this.getCompletionPercentage() + '%';
        });
        
        // Update stats
        this.updateStats();
        
        // Update progress circles
        this.updateProgressCircles();
    },
    
    updateStats: function() {
        const stats = {
            completedStages: AppState.progress.completedStages.length,
            timeSpent: Math.round(AppState.progress.timeSpent / 60) + 'h',
            achievements: AppState.progress.achievements.length,
            projectsStarted: AppState.progress.projectsStarted
        };
        
        Object.keys(stats).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = stats[key];
            }
        });
    },
    
    updateProgressCircles: function() {
        const circles = document.querySelectorAll('.progress-circle');
        const percentage = this.getCompletionPercentage();
        
        circles.forEach(circle => {
            const degrees = (percentage / 100) * 360;
            circle.style.background = `conic-gradient(var(--primary-color) ${degrees}deg, var(--light-color) ${degrees}deg)`;
        });
    }
};

// Activity Management
const ActivityManager = {
    activities: [],
    
    init: function() {
        const savedActivities = Storage.load('activities');
        if (savedActivities) {
            this.activities = savedActivities;
        }
        this.updateActivityList();
    },
    
    addActivity: function(text, type = 'general') {
        const activity = {
            id: Date.now(),
            text: text,
            type: type,
            timestamp: new Date().toISOString()
        };
        
        this.activities.unshift(activity);
        
        // Keep only last 50 activities
        if (this.activities.length > 50) {
            this.activities = this.activities.slice(0, 50);
        }
        
        Storage.save('activities', this.activities);
        this.updateActivityList();
    },
    
    updateActivityList: function() {
        const activityList = document.getElementById('activityList');
        if (!activityList) return;
        
        if (this.activities.length === 0) {
            activityList.innerHTML = `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-info-circle text-info"></i>
                    </div>
                    <div class="activity-content">
                        <p class="activity-text">No recent activity</p>
                        <small class="activity-time">Start learning to see your progress here</small>
                    </div>
                </div>
            `;
            return;
        }
        
        const activityHTML = this.activities.slice(0, 10).map(activity => {
            const timeAgo = this.getTimeAgo(activity.timestamp);
            const icon = this.getActivityIcon(activity.type);
            
            return `
                <div class="activity-item">
                    <div class="activity-icon">
                        ${icon}
                    </div>
                    <div class="activity-content">
                        <p class="activity-text">${activity.text}</p>
                        <small class="activity-time">${timeAgo}</small>
                    </div>
                </div>
            `;
        }).join('');
        
        activityList.innerHTML = activityHTML;
    },
    
    getActivityIcon: function(type) {
        const icons = {
            achievement: '<i class="fas fa-trophy text-warning"></i>',
            stage: '<i class="fas fa-check-circle text-success"></i>',
            project: '<i class="fas fa-project-diagram text-info"></i>',
            general: '<i class="fas fa-info-circle text-primary"></i>'
        };
        return icons[type] || icons.general;
    },
    
    getTimeAgo: function(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInMinutes = Math.floor((now - time) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours} hours ago`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays} days ago`;
    }
};

// Dashboard Management
const Dashboard = {
    init: function() {
        this.generateStagesGrid();
        this.setupEventListeners();
    },
    
    generateStagesGrid: function() {
        const stagesGrid = document.getElementById('stagesGrid');
        if (!stagesGrid) return;
        
        const stagesHTML = STAGES_DATA.map(stage => {
            const isCompleted = AppState.progress.completedStages.includes(stage.id);
            const progressWidth = isCompleted ? 100 : 0;
            const statusClass = isCompleted ? 'completed' : 'not-started';
            const statusText = isCompleted ? 'Completed' : 'Not Started';
            
            return `
                <div class="col-md-6 col-lg-4">
                    <div class="stage-card ${statusClass}">
                        <div class="stage-card-header">
                            <div class="stage-card-number">Stage ${stage.id}</div>
                            <h5 class="stage-card-title">${stage.title}</h5>
                        </div>
                        <div class="stage-card-body">
                            <p class="stage-card-description">${stage.description}</p>
                            <div class="stage-card-progress">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">Progress</small>
                                    <small class="text-muted">${progressWidth}%</small>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar" style="width: ${progressWidth}%"></div>
                                </div>
                            </div>
                            <div class="stage-card-actions">
                                <a href="stage-${String(stage.id).padStart(2, '0')}.html" class="btn btn-primary btn-sm">
                                    ${isCompleted ? 'Review' : 'Start'}
                                </a>
                                <span class="badge bg-secondary">${stage.category}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
        
        stagesGrid.innerHTML = stagesHTML;
    },
    
    setupEventListeners: function() {
        // Add any dashboard-specific event listeners here
    }
};

// Global Functions (called from HTML)
function markStageAsVisited(stageId) {
    ProgressManager.markStageAsVisited(stageId);
    ActivityManager.addActivity(`Visited Stage ${stageId}: ${STAGES_DATA[stageId - 1]?.title}`, 'stage');
}

function saveReflections(stageId) {
    const reflections = {};
    const textareas = document.querySelectorAll('.reflection-questions textarea');
    
    textareas.forEach((textarea, index) => {
        reflections[`question_${index + 1}`] = textarea.value;
    });
    
    Storage.save(`reflections_stage_${stageId}`, reflections);
    
    // Show success message
    showNotification('Reflections saved successfully!', 'success');
    
    // Add activity
    ActivityManager.addActivity(`Saved reflections for Stage ${stageId}`, 'general');
}

function saveMarketAnalysis(stageId) {
    const analysis = {
        therapeuticArea: document.getElementById('therapeuticArea')?.value,
        unmetNeed: document.querySelector('textarea[placeholder*="unmet need"]')?.value,
        marketSize: document.querySelector('select[placeholder*="market size"]')?.value
    };
    
    Storage.save(`market_analysis_stage_${stageId}`, analysis);
    showNotification('Market analysis saved successfully!', 'success');
    ActivityManager.addActivity(`Completed market analysis for Stage ${stageId}`, 'general');
}

function saveProcessPlan(stageId) {
    const plan = {
        projectType: document.getElementById('projectType')?.value,
        methodology: document.querySelector('input[name="methodology"]:checked')?.value,
        rationale: document.querySelector('textarea[placeholder*="methodology"]')?.value
    };
    
    Storage.save(`process_plan_stage_${stageId}`, plan);
    showNotification('Process plan saved successfully!', 'success');
    ActivityManager.addActivity(`Created process plan for Stage ${stageId}`, 'general');
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function downloadResource(resourceId) {
    // Simulate resource download
    showNotification(`Downloading ${resourceId.replace('-', ' ')}...`, 'info');
    ActivityManager.addActivity(`Downloaded resource: ${resourceId}`, 'general');
}

function downloadTemplate(templateId) {
    // Simulate template download
    showNotification(`Downloading ${templateId.replace('-', ' ')} template...`, 'info');
    ActivityManager.addActivity(`Downloaded template: ${templateId}`, 'general');
}

// Resource Functions
function openGlossary() {
    showNotification('Opening glossary...', 'info');
    // In a real implementation, this would open a modal or new page
}

function openCaseStudies() {
    showNotification('Opening case studies...', 'info');
}

function openVideoLibrary() {
    showNotification('Opening video library...', 'info');
}

function openQuizzes() {
    showNotification('Opening practice quizzes...', 'info');
}

function openCostCalculator() {
    showNotification('Opening cost calculator...', 'info');
}

function openTimelinePlanner() {
    showNotification('Opening timeline planner...', 'info');
}

function openRiskAssessment() {
    showNotification('Opening risk assessment tool...', 'info');
}

function openMarketAnalysis() {
    showNotification('Opening market analysis tool...', 'info');
}

function openProjectTemplates() {
    showNotification('Opening project templates...', 'info');
}

function openHelp() {
    showNotification('Opening help & support...', 'info');
}

// Initialize Dashboard
function initializeDashboard() {
    Dashboard.init();
}

// Application Initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize core systems
    ProgressManager.init();
    ActivityManager.init();

    // Initialize page-specific functionality
    if (document.getElementById('stagesGrid')) {
        Dashboard.init();
    }

    // Initialize stage content if on a stage page
    if (window.UIBuilder && typeof window.UIBuilder.initializeStageContent === 'function') {
        window.UIBuilder.initializeStageContent();
    }

    // Track time spent (simple implementation)
    let startTime = Date.now();
    window.addEventListener('beforeunload', function() {
        const timeSpent = Math.floor((Date.now() - startTime) / 1000 / 60); // minutes
        ProgressManager.addTimeSpent(timeSpent);
    });

    // Add welcome activity for new users
    if (AppState.progress.completedStages.length === 0 && ActivityManager.activities.length === 0) {
        const welcomeMessage = window.I18n ?
            window.I18n.getTranslation('app_title') + ' - ' + (window.I18n.getCurrentLanguage() === 'ar' ? 'مرحباً بك!' : 'Welcome!') :
            'Welcome to the Medical Device Innovation Guide!';
        ActivityManager.addActivity(welcomeMessage, 'general');
    }

    // Listen for language changes and update UI
    window.addEventListener('languageChanged', function(event) {
        // Update any dynamic content that needs language refresh
        if (window.UIBuilder && document.getElementById('stagesGrid')) {
            window.UIBuilder.updateDashboardStages();
        }

        // Update progress manager UI
        ProgressManager.updateUI();
    });
});

// Export for use in other modules
window.MedDeviceApp = {
    AppState,
    ProgressManager,
    ActivityManager,
    Dashboard,
    Storage
};
