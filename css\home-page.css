/**
 * Medical Device Innovation Guide - Home Page Specific Styles
 * Fixes for overlapping and interactive elements
 */

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: hidden;
}

/* Navigation Space Management */
.hero-section {
    margin-top: 60px !important;
    padding: 4rem 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    position: relative !important;
    z-index: 1 !important;
    min-height: calc(100vh - 60px) !important;
    display: flex !important;
    align-items: center !important;
}

/* Ensure all sections have proper spacing */
section {
    position: relative !important;
    z-index: 1 !important;
    clear: both !important;
}

section:not(.hero-section) {
    margin-top: 0 !important;
}

/* Container fixes */
.container {
    position: relative !important;
    z-index: 1 !important;
    width: 100% !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 15px !important;
}

/* Hero Content Styling */
.hero-content {
    animation: fadeInUp 1s ease-out;
    position: relative;
    z-index: 2;
}

.hero-visual {
    animation: fadeInRight 1s ease-out 0.3s both;
    position: relative;
    z-index: 2;
}

/* Hero Stats */
.hero-stats {
    margin: 2rem 0;
}

.hero-stats .stat-item {
    padding: 1rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-stats .stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

/* Interactive Buttons */
.interactive-btn {
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    border: none !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    margin: 0.25rem !important;
}

.interactive-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    text-decoration: none !important;
}

.interactive-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.interactive-btn:hover::before {
    left: 100%;
}

/* Innovation Diagram */
.innovation-diagram {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto;
}

.diagram-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.diagram-orbit {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.orbit-1 {
    width: 120px;
    height: 120px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.orbit-2 {
    width: 180px;
    height: 180px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 30s;
}

.orbit-3 {
    width: 240px;
    height: 240px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 40s;
}

.orbit-item {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: counter-rotate 20s linear infinite reverse;
    backdrop-filter: blur(10px);
}

.orbit-2 .orbit-item {
    animation-duration: 30s;
}

.orbit-3 .orbit-item {
    animation-duration: 40s;
}

.orbit-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.orbit-1 .orbit-item {
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
}

.orbit-2 .orbit-item {
    top: -30px;
    right: -30px;
}

.orbit-3 .orbit-item {
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes counter-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(-360deg); }
}

/* Stage Group Cards */
.stage-group-card {
    background: white !important;
    border-radius: 16px !important;
    padding: 2rem !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    position: relative !important;
    overflow: hidden !important;
    border: 2px solid transparent !important;
    margin-bottom: 2rem !important;
    height: 100% !important;
}

.stage-group-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
    border-color: #667eea !important;
}

.stage-group-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stage-group-card:hover::before {
    transform: scaleX(1);
}

.stage-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.stage-group-card:hover .stage-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0 !important;
        min-height: calc(100vh - 60px) !important;
    }
    
    .innovation-diagram {
        width: 250px;
        height: 250px;
    }
    
    .orbit-1 { width: 100px; height: 100px; }
    .orbit-2 { width: 150px; height: 150px; }
    .orbit-3 { width: 200px; height: 200px; }
    
    .orbit-item {
        width: 50px;
        height: 50px;
        font-size: 0.7rem;
    }
    
    .stage-group-card {
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
    }
    
    .stage-icon {
        width: 60px;
        height: 60px;
    }
    
    .interactive-btn {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
        margin: 0.125rem !important;
    }
}

/* RTL Support */
html[dir="rtl"] .hero-visual {
    animation: fadeInLeft 1s ease-out 0.3s both;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Ensure proper stacking */
.enhanced-navigation {
    z-index: 1050 !important;
}

.hero-section * {
    position: relative;
    z-index: 1;
}

/* Fix any remaining overlap issues */
.py-5 {
    position: relative !important;
    z-index: 1 !important;
}

.bg-light {
    position: relative !important;
    z-index: 1 !important;
}

.bg-primary {
    position: relative !important;
    z-index: 1 !important;
}
