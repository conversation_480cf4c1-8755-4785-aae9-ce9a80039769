<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n-key="stage_2_title">Stage 2: Medical Device Landscape</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
</head>
<body data-stage-key="stage_02_landscape">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-heartbeat me-2"></i>
                <span data-i18n-key="app_title">Medical Device Innovation Guide</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" data-i18n-key="menu_toggle" title="Toggle navigation menu">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html" data-i18n-key="nav_home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html" data-i18n-key="nav_dashboard">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html" data-i18n-key="nav_resources">Resources</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm" id="languageToggle" data-i18n-key="language_toggle" title="Switch language">
                            <i class="fas fa-globe me-2"></i>
                            <span class="current-lang">EN</span>
                            <span class="separator">|</span>
                            <span class="other-lang">ع</span>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="stage-content">
        <div class="container-fluid">
            <!-- Stage Header -->
            <div class="stage-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="stage-breadcrumb">
                            <a href="dashboard.html" data-i18n-key="nav_dashboard">Dashboard</a> / <span data-i18n-key="stage_2_title">Stage 2</span>
                        </div>
                        <h1 class="stage-title" data-i18n-key="stage_2_title">Stage 2: Medical Device Landscape</h1>
                        <p class="stage-subtitle">Understanding the Current State and Future Opportunities</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="stage-progress">
                            <div class="progress-circle-small">
                                <span class="stage-number">2</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Guide Column -->
                <div class="col-lg-6 guide-column">
                    <div class="guide-header mb-4">
                        <h2 data-i18n-key="nav_guide">Guide</h2>
                        <p class="text-muted">Market landscape and industry overview</p>
                    </div>
                    
                    <div class="accordion" id="guideAccordion">
                        <!-- Content will be dynamically loaded here based on language -->
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden" data-i18n-key="loading">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted" data-i18n-key="loading">Loading content...</p>
                        </div>
                    </div>
                </div>

                <!-- Workspace Column -->
                <div class="col-lg-6 workspace-column">
                    <div class="workspace-header mb-4">
                        <h2 data-i18n-key="nav_workspace">Workspace</h2>
                        <p class="text-muted">Market analysis and competitive research</p>
                        
                        <div class="stage-navigation">
                            <a href="stage-01-bilingual.html" class="btn btn-outline-secondary">
                                <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
                            </a>
                            <a href="stage-03-bilingual.html" class="btn btn-primary">
                                <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
                            </a>
                        </div>
                    </div>
                    
                    <div class="workspace-content">
                        <!-- Learning Objectives -->
                        <div class="workspace-section mb-4">
                            <h4 data-i18n-key="learning_objectives">Learning Objectives</h4>
                            <div class="objectives-list">
                                <div class="objective-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>Understand the current medical device market size and growth trends</span>
                                </div>
                                <div class="objective-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>Identify different categories and classifications of medical devices</span>
                                </div>
                                <div class="objective-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>Recognize key players and stakeholders in the medical device ecosystem</span>
                                </div>
                                <div class="objective-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>Analyze emerging trends and future opportunities</span>
                                </div>
                            </div>
                        </div>

                        <!-- Market Analysis Tool -->
                        <div class="workspace-section mb-4">
                            <h4 data-i18n-key="market_analysis">Market Analysis Tool</h4>
                            <div class="exercise-card">
                                <h5>Identify Market Opportunities</h5>
                                <p>Select a therapeutic area and analyze its market potential:</p>
                                <div class="market-analysis-tool">
                                    <div class="mb-3">
                                        <label class="form-label">Select Therapeutic Area:</label>
                                        <select class="form-select" id="therapeuticArea">
                                            <option value="">Choose an area...</option>
                                            <option value="cardiovascular">Cardiovascular</option>
                                            <option value="neurology">Neurology</option>
                                            <option value="oncology">Oncology</option>
                                            <option value="diabetes">Diabetes Care</option>
                                            <option value="respiratory">Respiratory</option>
                                            <option value="orthopedics">Orthopedics</option>
                                            <option value="ophthalmology">Ophthalmology</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Market Size Estimate:</label>
                                        <select class="form-select" id="marketSize">
                                            <option value="">Select market size...</option>
                                            <option value="small">Small (&lt;$100M)</option>
                                            <option value="medium">Medium ($100M-$1B)</option>
                                            <option value="large">Large ($1B-$10B)</option>
                                            <option value="mega">Mega ($10B+)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Growth Rate:</label>
                                        <select class="form-select" id="growthRate">
                                            <option value="">Select growth rate...</option>
                                            <option value="low">Low (&lt;5% annually)</option>
                                            <option value="medium">Medium (5-15% annually)</option>
                                            <option value="high">High (&gt;15% annually)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Key Market Drivers:</label>
                                        <textarea class="form-control" rows="3" id="marketDrivers" placeholder="Describe the main factors driving growth in this market..."></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Major Competitors:</label>
                                        <textarea class="form-control" rows="2" id="competitors" placeholder="List key companies in this space..."></textarea>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="saveMarketAnalysis(2)">
                                    <i class="fas fa-save me-2"></i><span data-i18n-key="save">Save Analysis</span>
                                </button>
                                <button class="btn btn-outline-info ms-2" onclick="generateMarketReport()">
                                    <i class="fas fa-chart-bar me-2"></i>Generate Report
                                </button>
                            </div>
                        </div>

                        <!-- Market Insights -->
                        <div class="workspace-section mb-4">
                            <h5>Market Insights</h5>
                            <div id="marketInsights" class="insights-container">
                                <div class="insight-card">
                                    <h6><i class="fas fa-globe me-2"></i>Global Market Size</h6>
                                    <p class="mb-1"><strong>$500+ Billion</strong> - Total medical device market</p>
                                    <small class="text-muted">Growing at 6.1% annually</small>
                                </div>
                                <div class="insight-card">
                                    <h6><i class="fas fa-chart-line me-2"></i>Fastest Growing Segments</h6>
                                    <ul class="mb-0">
                                        <li>Digital Health: 25% growth</li>
                                        <li>AI-enabled devices: 30% growth</li>
                                        <li>Remote monitoring: 20% growth</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Tracker -->
                        <div class="workspace-section">
                            <h5 data-i18n-key="your_progress">Your Progress</h5>
                            <div class="progress mb-2">
                                <div class="progress-bar" style="width: 10%"></div>
                            </div>
                            <small class="text-muted">Stage 2 of 20 (10%)</small>
                        </div>

                        <!-- Quick Navigation -->
                        <div class="workspace-section mt-4">
                            <h5 data-i18n-key="quick_navigation">Quick Navigation</h5>
                            <div class="nav-links">
                                <a href="stage-01-bilingual.html" class="nav-link-item">
                                    <span class="nav-number">1</span>
                                    <span class="nav-title" data-i18n-key="stage_1_title">General Introduction</span>
                                </a>
                                <a href="stage-03-bilingual.html" class="nav-link-item">
                                    <span class="nav-number">3</span>
                                    <span class="nav-title" data-i18n-key="stage_3_title">Innovation Process</span>
                                </a>
                                <a href="stage-04-bilingual.html" class="nav-link-item">
                                    <span class="nav-number">4</span>
                                    <span class="nav-title" data-i18n-key="stage_4_title">Understanding Opportunity</span>
                                </a>
                            </div>
                        </div>

                        <!-- Resources -->
                        <div class="workspace-section mt-4">
                            <h5 data-i18n-key="related_resources">Related Resources</h5>
                            <div class="resource-links">
                                <a href="#" class="resource-link" onclick="downloadResource('market-data-report')">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Market Data Report</span>
                                </a>
                                <a href="#" class="resource-link" onclick="downloadResource('classification-guide')">
                                    <i class="fas fa-file-pdf"></i>
                                    <span>Device Classification Guide</span>
                                </a>
                                <a href="#" class="resource-link" onclick="openMarketAnalysisTool()">
                                    <i class="fas fa-calculator"></i>
                                    <span>Advanced Market Analysis</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/i18n.js"></script>
    <script src="js/i18n-advanced.js"></script>
    <script src="js/content-store.js"></script>
    <script src="js/ui-builder.js"></script>
    <script src="js/voice-narration.js"></script>
    <script src="js/analytics-performance.js"></script>
    <script src="main.js"></script>
    <script>
        // Initialize stage content
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize content rendering
            if (window.UIBuilder) {
                window.UIBuilder.initializeStageContent();
            }
            
            // Mark stage as visited
            if (window.markStageAsVisited) {
                markStageAsVisited(2);
            }
        });

        // Market analysis functions
        function generateMarketReport() {
            const therapeuticArea = document.getElementById('therapeuticArea').value;
            const marketSize = document.getElementById('marketSize').value;
            const growthRate = document.getElementById('growthRate').value;
            
            if (!therapeuticArea || !marketSize || !growthRate) {
                if (window.showNotification) {
                    window.showNotification('Please fill in all market analysis fields', 'warning');
                }
                return;
            }
            
            // Generate insights based on selections
            const insights = document.getElementById('marketInsights');
            insights.innerHTML = `
                <div class="insight-card">
                    <h6><i class="fas fa-target me-2"></i>${therapeuticArea.charAt(0).toUpperCase() + therapeuticArea.slice(1)} Market</h6>
                    <p class="mb-1"><strong>Size:</strong> ${getMarketSizeText(marketSize)}</p>
                    <p class="mb-1"><strong>Growth:</strong> ${getGrowthRateText(growthRate)}</p>
                    <small class="text-muted">Analysis generated based on your inputs</small>
                </div>
                <div class="insight-card">
                    <h6><i class="fas fa-lightbulb me-2"></i>Opportunities</h6>
                    <p class="mb-0">${getOpportunityText(therapeuticArea, marketSize, growthRate)}</p>
                </div>
            `;
            
            if (window.showNotification) {
                window.showNotification('Market report generated successfully!', 'success');
            }
        }
        
        function getMarketSizeText(size) {
            const sizes = {
                'small': 'Small market with focused opportunities',
                'medium': 'Medium-sized market with good potential',
                'large': 'Large market with significant opportunities',
                'mega': 'Mega market with massive potential'
            };
            return sizes[size] || 'Unknown market size';
        }
        
        function getGrowthRateText(rate) {
            const rates = {
                'low': 'Stable, mature market',
                'medium': 'Steady growth expected',
                'high': 'Rapid expansion anticipated'
            };
            return rates[rate] || 'Unknown growth rate';
        }
        
        function getOpportunityText(area, size, rate) {
            if (size === 'large' && rate === 'high') {
                return 'Excellent opportunity with high growth potential in a large market.';
            } else if (size === 'medium' && rate === 'medium') {
                return 'Good opportunity with steady growth in an established market.';
            } else if (size === 'small' && rate === 'high') {
                return 'Niche opportunity with high growth potential.';
            } else {
                return 'Consider market dynamics and competitive landscape carefully.';
            }
        }
        
        function openMarketAnalysisTool() {
            if (window.showNotification) {
                window.showNotification('Opening advanced market analysis tool...', 'info');
            }
        }
    </script>
</body>
</html>
