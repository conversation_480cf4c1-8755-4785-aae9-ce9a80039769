import React from 'react';

interface ProgressBarProps {
  currentChapter: number;
  totalChapters: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ currentChapter, totalChapters }) => {
  const progress = ((currentChapter + 1) / totalChapters) * 100;
  
  return (
    <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
      <div 
        className="h-full bg-gradient-to-r from-blue-600 to-indigo-600 transition-all duration-300 ease-out"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
};

export default ProgressBar;