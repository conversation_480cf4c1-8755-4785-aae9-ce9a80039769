/**
 * Medical Device Innovation Guide - Workspace Tools CSS
 * Styles for interactive workspace components
 */

/* General Workspace Styles */
.workspace-column {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    min-height: 100vh;
}

.workspace-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.workspace-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.exercise-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.objectives-list .objective-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

/* Stage Navigation */
.stage-navigation {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

/* Process Map Visualization */
.process-steps-visualization {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.process-step-item {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.process-step-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.step-number {
    background: #007bff;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h6 {
    margin-bottom: 0.25rem;
    color: #333;
}

.step-duration {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.step-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0;
}

/* Stage Gates */
.stage-gates {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.gate-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.gate-item:hover {
    border-color: #007bff;
}

.gate-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.gate-status {
    font-size: 0.75rem;
}

.gate-criteria {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Assessment Matrix */
.assessment-matrix {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

.assessment-category {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.factor-rating {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.factor-rating label {
    min-width: 120px;
    margin-bottom: 0;
    font-size: 0.875rem;
}

.rating-scale {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.rating-value {
    min-width: 20px;
    text-align: center;
    font-weight: bold;
    color: #007bff;
}

/* SWOT Matrix */
.swot-matrix {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.swot-quadrant {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.swot-quadrant h6 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

/* Stakeholder Management */
.stakeholder-categories {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stakeholder-category {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.stakeholder-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Needs Prioritization Matrix */
.priority-matrix {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.matrix-quadrant {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    min-height: 150px;
}

.matrix-quadrant.high-impact-high-freq {
    border-color: #dc3545;
    background: #fff5f5;
}

.matrix-quadrant.high-impact-low-freq {
    border-color: #fd7e14;
    background: #fff8f0;
}

.matrix-quadrant.low-impact-high-freq {
    border-color: #ffc107;
    background: #fffbf0;
}

.matrix-quadrant.low-impact-low-freq {
    border-color: #6c757d;
    background: #f8f9fa;
}

.need-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: between;
    align-items: center;
    font-size: 0.875rem;
}

/* Brainstorming Interface */
.brainstorming-area {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.session-timer {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    margin-bottom: 1rem;
}

.ideas-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
}

.idea-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

/* Concept Development */
.concept-builder {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.feature-item, .task-item, .endpoint-item, .question-item {
    margin-bottom: 0.5rem;
}

/* Evaluation Matrix */
.evaluation-matrix {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.criteria-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.criterion {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.criterion label {
    min-width: 150px;
    margin-bottom: 0;
}

/* Test Design */
.test-designer {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.metrics-checklist {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

/* Test Results */
.results-tracker {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.session-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.participant-data {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

/* Clinical Study Design */
.study-design {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

/* Regulatory Planning */
.regulatory-planner {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.regulatory-timeline {
    margin-top: 1rem;
}

.timeline-visualization {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.timeline-step {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

/* Patient Recruitment */
.recruitment-planner {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.recruitment-channels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

/* FMEA Analysis */
.fmea-builder {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.rpn-calculation {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.fmea-table {
    margin-top: 1rem;
}

/* Risk Control Hierarchy */
.control-hierarchy {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.control-level {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.control-level[data-level="1"] {
    border-left: 4px solid #28a745;
}

.control-level[data-level="2"] {
    border-left: 4px solid #ffc107;
}

.control-level[data-level="3"] {
    border-left: 4px solid #17a2b8;
}

.measure-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Reliability Testing */
.reliability-planner {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.testing-schedule {
    margin-top: 1rem;
}

.test-schedule-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.test-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .assessment-matrix,
    .swot-matrix,
    .stakeholder-categories,
    .priority-matrix {
        grid-template-columns: 1fr;
    }
    
    .metrics-checklist,
    .recruitment-channels {
        grid-template-columns: 1fr;
    }
    
    .factor-rating,
    .criterion {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .factor-rating label,
    .criterion label {
        min-width: auto;
    }
}

/* RTL Support */
html[dir="rtl"] .process-step-item {
    flex-direction: row-reverse;
}

html[dir="rtl"] .step-number {
    margin-right: 0;
    margin-left: 1rem;
}

html[dir="rtl"] .gate-header,
html[dir="rtl"] .need-item,
html[dir="rtl"] .idea-item {
    flex-direction: row-reverse;
}

html[dir="rtl"] .stakeholder-item,
html[dir="rtl"] .measure-item {
    flex-direction: row-reverse;
}

html[dir="rtl"] .timeline-step {
    flex-direction: row-reverse;
}

/* Enhanced Navigation Styles */
.enhanced-navigation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-bottom: 1px solid #e9ecef;
}

.enhanced-nav-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand .brand-link {
    color: white;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: bold;
    display: flex;
    align-items: center;
}

.nav-brand .brand-link:hover {
    color: #f8f9fa;
}

.nav-menu {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.nav-section h6 {
    margin: 0;
    font-size: 0.75rem;
    text-transform: uppercase;
    opacity: 0.8;
    letter-spacing: 0.5px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

.nav-link.active {
    background: rgba(255,255,255,0.2);
    font-weight: 600;
}

.stage-number {
    background: rgba(255,255,255,0.2);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    margin-right: 0.5rem;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
}

.quick-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: rgba(255,255,255,0.2);
}

/* Search Panel */
.search-panel {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    border-color: #667eea;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1001;
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #333;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background: #f8f9fa;
    color: #333;
}

.result-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.result-desc {
    font-size: 0.875rem;
    color: #6c757d;
}

.no-results {
    padding: 1rem;
    text-align: center;
    color: #6c757d;
    margin: 0;
}

/* Bookmarks Panel */
.bookmarks-panel {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.bookmarks-container {
    max-width: 600px;
    margin: 0 auto;
}

.bookmarks-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Breadcrumbs */
.breadcrumb-container {
    background: #f8f9fa;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    margin-top: 60px; /* Account for fixed nav */
}

.breadcrumb-nav {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 0.875rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: 0.5rem;
    color: #6c757d;
}

.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* Floating Action Button */
.floating-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.fab-container {
    position: relative;
}

.fab-main {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.fab-main:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.fab-menu {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.fab-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-item {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #6c757d;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fab-item:hover {
    background: #5a6268;
    transform: scale(1.1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        flex-direction: column;
        padding: 1rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: block;
    }

    .nav-section {
        width: 100%;
        margin-bottom: 1rem;
    }

    .floating-actions {
        bottom: 1rem;
        right: 1rem;
    }

    .breadcrumb-container {
        margin-top: 70px;
    }
}

/* RTL Support for Navigation */
html[dir="rtl"] .nav-container {
    flex-direction: row-reverse;
}

html[dir="rtl"] .nav-link {
    flex-direction: row-reverse;
}

html[dir="rtl"] .stage-number {
    margin-right: 0;
    margin-left: 0.5rem;
}

html[dir="rtl"] .floating-actions {
    right: auto;
    left: 2rem;
}

html[dir="rtl"] .fab-menu {
    right: auto;
    left: 0;
}

html[dir="rtl"] .breadcrumb-item:not(:last-child)::after {
    margin-left: 0;
    margin-right: 0.5rem;
}
