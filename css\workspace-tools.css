/**
 * Medical Device Innovation Guide - Workspace Tools CSS
 * Styles for interactive workspace components
 */

/* General Workspace Styles */
.workspace-column {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    min-height: 100vh;
}

.workspace-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.workspace-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.exercise-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.objectives-list .objective-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

/* Stage Navigation */
.stage-navigation {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

/* Process Map Visualization */
.process-steps-visualization {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.process-step-item {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.process-step-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.step-number {
    background: #007bff;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h6 {
    margin-bottom: 0.25rem;
    color: #333;
}

.step-duration {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.step-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0;
}

/* Stage Gates */
.stage-gates {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.gate-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.gate-item:hover {
    border-color: #007bff;
}

.gate-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.gate-status {
    font-size: 0.75rem;
}

.gate-criteria {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Assessment Matrix */
.assessment-matrix {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

.assessment-category {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.factor-rating {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.factor-rating label {
    min-width: 120px;
    margin-bottom: 0;
    font-size: 0.875rem;
}

.rating-scale {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.rating-value {
    min-width: 20px;
    text-align: center;
    font-weight: bold;
    color: #007bff;
}

/* SWOT Matrix */
.swot-matrix {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.swot-quadrant {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.swot-quadrant h6 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

/* Stakeholder Management */
.stakeholder-categories {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stakeholder-category {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.stakeholder-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Needs Prioritization Matrix */
.priority-matrix {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.matrix-quadrant {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    min-height: 150px;
}

.matrix-quadrant.high-impact-high-freq {
    border-color: #dc3545;
    background: #fff5f5;
}

.matrix-quadrant.high-impact-low-freq {
    border-color: #fd7e14;
    background: #fff8f0;
}

.matrix-quadrant.low-impact-high-freq {
    border-color: #ffc107;
    background: #fffbf0;
}

.matrix-quadrant.low-impact-low-freq {
    border-color: #6c757d;
    background: #f8f9fa;
}

.need-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: between;
    align-items: center;
    font-size: 0.875rem;
}

/* Brainstorming Interface */
.brainstorming-area {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.session-timer {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    margin-bottom: 1rem;
}

.ideas-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
}

.idea-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

/* Concept Development */
.concept-builder {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.feature-item, .task-item, .endpoint-item, .question-item {
    margin-bottom: 0.5rem;
}

/* Evaluation Matrix */
.evaluation-matrix {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.criteria-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.criterion {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.criterion label {
    min-width: 150px;
    margin-bottom: 0;
}

/* Test Design */
.test-designer {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.metrics-checklist {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

/* Test Results */
.results-tracker {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.session-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.participant-data {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

/* Clinical Study Design */
.study-design {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

/* Regulatory Planning */
.regulatory-planner {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.regulatory-timeline {
    margin-top: 1rem;
}

.timeline-visualization {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.timeline-step {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

/* Patient Recruitment */
.recruitment-planner {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.recruitment-channels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

/* FMEA Analysis */
.fmea-builder {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.rpn-calculation {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.fmea-table {
    margin-top: 1rem;
}

/* Risk Control Hierarchy */
.control-hierarchy {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.control-level {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.control-level[data-level="1"] {
    border-left: 4px solid #28a745;
}

.control-level[data-level="2"] {
    border-left: 4px solid #ffc107;
}

.control-level[data-level="3"] {
    border-left: 4px solid #17a2b8;
}

.measure-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Reliability Testing */
.reliability-planner {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.testing-schedule {
    margin-top: 1rem;
}

.test-schedule-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.test-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .assessment-matrix,
    .swot-matrix,
    .stakeholder-categories,
    .priority-matrix {
        grid-template-columns: 1fr;
    }
    
    .metrics-checklist,
    .recruitment-channels {
        grid-template-columns: 1fr;
    }
    
    .factor-rating,
    .criterion {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .factor-rating label,
    .criterion label {
        min-width: auto;
    }
}

/* RTL Support */
html[dir="rtl"] .process-step-item {
    flex-direction: row-reverse;
}

html[dir="rtl"] .step-number {
    margin-right: 0;
    margin-left: 1rem;
}

html[dir="rtl"] .gate-header,
html[dir="rtl"] .need-item,
html[dir="rtl"] .idea-item {
    flex-direction: row-reverse;
}

html[dir="rtl"] .stakeholder-item,
html[dir="rtl"] .measure-item {
    flex-direction: row-reverse;
}

html[dir="rtl"] .timeline-step {
    flex-direction: row-reverse;
}
