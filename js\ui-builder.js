/**
 * Medical Device Innovation Guide - UI Builder
 * Handles dynamic content rendering based on language and stage
 */

/**
 * Render guide content for a specific stage
 * @param {string} stageKey - The stage key (e.g., 'stage_01_introduction')
 * @param {string} lang - Language code ('en' or 'ar')
 * @param {string} containerId - ID of the container element
 */
function renderGuideContent(stageKey, lang = 'en', containerId = 'guideAccordion') {
    const container = document.getElementById(containerId);
    if (!container) {
        console.warn(`Container with ID '${containerId}' not found`);
        return;
    }

    // Get content from content store
    const content = window.ContentStore && window.ContentStore[lang] && window.ContentStore[lang][stageKey];
    if (!content || !Array.isArray(content)) {
        console.warn(`No content found for stage '${stageKey}' in language '${lang}'`);
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                ${lang === 'ar' ? 'المحتوى قيد التطوير' : 'Content under development'}
            </div>
        `;
        return;
    }

    // Generate accordion HTML
    let accordionHTML = '';
    content.forEach((section, index) => {
        const sectionId = `section-${stageKey}-${index}`;
        const isFirstSection = index === 0;
        
        accordionHTML += `
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading-${sectionId}">
                    <button class="accordion-button ${isFirstSection ? '' : 'collapsed'}" 
                            type="button" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#collapse-${sectionId}" 
                            aria-expanded="${isFirstSection ? 'true' : 'false'}" 
                            aria-controls="collapse-${sectionId}">
                        ${section.title}
                    </button>
                </h2>
                <div id="collapse-${sectionId}" 
                     class="accordion-collapse collapse ${isFirstSection ? 'show' : ''}" 
                     aria-labelledby="heading-${sectionId}" 
                     data-bs-parent="#${containerId}">
                    <div class="accordion-body">
                        ${section.content}
                    </div>
                </div>
            </div>
        `;
    });

    // Inject the HTML into the container
    container.innerHTML = accordionHTML;
    
    console.log(`Rendered ${content.length} sections for ${stageKey} in ${lang}`);
}

/**
 * Get stage key from current page
 * @returns {string} Stage key or null if not found
 */
function getCurrentStageKey() {
    // Try to get from body data attribute
    const bodyStageKey = document.body.getAttribute('data-stage-key');
    if (bodyStageKey) {
        return bodyStageKey;
    }

    // Try to extract from URL
    const path = window.location.pathname;
    const filename = path.split('/').pop();
    
    // Map filename to stage key
    const stageMapping = {
        'stage-01.html': 'stage_01_introduction',
        'stage-02.html': 'stage_02_landscape',
        'stage-03.html': 'stage_03_process',
        'stage-04.html': 'stage_04_opportunity',
        'stage-05.html': 'stage_05_needs'
    };
    
    return stageMapping[filename] || null;
}

/**
 * Initialize content rendering for stage pages
 */
function initializeStageContent() {
    const stageKey = getCurrentStageKey();
    if (!stageKey) {
        console.log('No stage key found, skipping content rendering');
        return;
    }

    const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
    renderGuideContent(stageKey, currentLang);
    
    // Listen for language changes
    window.addEventListener('languageChanged', function(event) {
        const newLang = event.detail.language;
        renderGuideContent(stageKey, newLang);
    });
}

/**
 * Create a bilingual stage page template
 * @param {Object} stageInfo - Stage information
 * @returns {string} HTML template
 */
function createStagePageTemplate(stageInfo) {
    const { id, titleKey, descriptionKey, prevStage, nextStage } = stageInfo;
    
    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title data-i18n-key="${titleKey}">Stage ${id}</title>
            
            <!-- Bootstrap CSS -->
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <!-- Font Awesome -->
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            <!-- Custom CSS -->
            <link href="style.css" rel="stylesheet">
        </head>
        <body data-stage-key="stage_${String(id).padStart(2, '0')}_${titleKey.split('_')[2]}">
            <!-- Navigation -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
                <div class="container">
                    <a class="navbar-brand" href="index.html">
                        <i class="fas fa-heartbeat me-2"></i>
                        <span data-i18n-key="app_title">Medical Device Innovation Guide</span>
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="index.html" data-i18n-key="nav_home">Home</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="dashboard.html" data-i18n-key="nav_dashboard">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="resources.html" data-i18n-key="nav_resources">Resources</a>
                            </li>
                        </ul>
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <button class="btn btn-outline-light btn-sm" id="languageToggle">
                                    <i class="fas fa-globe me-2"></i>
                                    <span class="current-lang">EN</span>
                                    <span class="separator">|</span>
                                    <span class="other-lang">ع</span>
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="stage-content">
                <div class="container-fluid">
                    <div class="row">
                        <!-- Guide Column -->
                        <div class="col-lg-6 guide-column">
                            <div class="guide-header">
                                <h2 data-i18n-key="nav_guide">Guide</h2>
                                <p data-i18n-key="${descriptionKey}">Stage description</p>
                            </div>
                            <div class="accordion" id="guideAccordion">
                                <!-- Content will be dynamically loaded here -->
                            </div>
                        </div>

                        <!-- Workspace Column -->
                        <div class="col-lg-6 workspace-column">
                            <div class="workspace-header">
                                <h2 data-i18n-key="nav_workspace">Workspace</h2>
                                <div class="stage-navigation">
                                    ${prevStage ? 
                                        `<a href="stage-${String(prevStage).padStart(2, '0')}.html" class="btn btn-outline-secondary">
                                            <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
                                        </a>` : 
                                        `<button class="btn btn-outline-secondary" disabled>
                                            <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
                                        </button>`
                                    }
                                    ${nextStage ? 
                                        `<a href="stage-${String(nextStage).padStart(2, '0')}.html" class="btn btn-primary">
                                            <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
                                        </a>` : 
                                        `<a href="dashboard.html" class="btn btn-success">
                                            <i class="fas fa-check"></i> <span data-i18n-key="complete">Complete</span>
                                        </a>`
                                    }
                                </div>
                            </div>
                            
                            <div class="workspace-content">
                                <!-- Interactive workspace content -->
                                <div class="workspace-section">
                                    <h4 data-i18n-key="interactive_exercise">Interactive Exercise</h4>
                                    <div class="exercise-placeholder">
                                        <p data-i18n-key="loading">Loading workspace...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <!-- Bootstrap JS -->
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
            <!-- Custom JS -->
            <script src="js/i18n.js"></script>
            <script src="js/content-store.js"></script>
            <script src="js/ui-builder.js"></script>
            <script src="main.js"></script>
            <script>
                // Initialize stage content
                document.addEventListener('DOMContentLoaded', function() {
                    initializeStageContent();
                    markStageAsVisited(${id});
                });
            </script>
        </body>
        </html>
    `;
}

/**
 * Update dashboard with bilingual stage cards
 */
function updateDashboardStages() {
    const stagesGrid = document.getElementById('stagesGrid');
    if (!stagesGrid) return;

    const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
    const stages = [
        { id: 1, key: 'stage_1_title', category: 'foundation' },
        { id: 2, key: 'stage_2_title', category: 'foundation' },
        { id: 3, key: 'stage_3_title', category: 'foundation' },
        { id: 4, key: 'stage_4_title', category: 'foundation' },
        { id: 5, key: 'stage_5_title', category: 'foundation' }
    ];

    let stagesHTML = '';
    stages.forEach(stage => {
        const isCompleted = window.MedDeviceApp && 
                           window.MedDeviceApp.AppState.progress.completedStages.includes(stage.id);
        const progressWidth = isCompleted ? 100 : 0;
        const title = window.I18n ? window.I18n.getTranslation(stage.key) : `Stage ${stage.id}`;
        const categoryText = window.I18n ? window.I18n.getTranslation(stage.category) : stage.category;
        const startText = window.I18n ? window.I18n.getTranslation('start') : 'Start';
        const reviewText = window.I18n ? window.I18n.getTranslation('review') : 'Review';

        stagesHTML += `
            <div class="col-md-6 col-lg-4">
                <div class="stage-card">
                    <div class="stage-card-header">
                        <div class="stage-card-number">${currentLang === 'ar' ? 'المرحلة' : 'Stage'} ${stage.id}</div>
                        <h5 class="stage-card-title">${title}</h5>
                    </div>
                    <div class="stage-card-body">
                        <div class="stage-card-progress">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">${window.I18n ? window.I18n.getTranslation('your_progress') : 'Progress'}</small>
                                <small class="text-muted">${progressWidth}%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar" style="width: ${progressWidth}%"></div>
                            </div>
                        </div>
                        <div class="stage-card-actions">
                            <a href="stage-${String(stage.id).padStart(2, '0')}.html" class="btn btn-primary btn-sm">
                                ${isCompleted ? reviewText : startText}
                            </a>
                            <span class="badge bg-secondary">${categoryText}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    stagesGrid.innerHTML = stagesHTML;
}

// Export functions for global use
window.UIBuilder = {
    renderGuideContent,
    getCurrentStageKey,
    initializeStageContent,
    createStagePageTemplate,
    updateDashboardStages
};

// Auto-initialize on DOM load
document.addEventListener('DOMContentLoaded', function() {
    // Update dashboard if on dashboard page
    if (document.getElementById('stagesGrid')) {
        updateDashboardStages();
        
        // Listen for language changes
        window.addEventListener('languageChanged', function() {
            updateDashboardStages();
        });
    }
});
