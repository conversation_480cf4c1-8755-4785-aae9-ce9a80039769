import React, { useState } from 'react';
import { Lecture } from '../types';
import { Play, Download, FileText, Video, BookOpen, CheckCircle, XCircle, RotateCcw } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface LectureContentProps {
  lecture: Lecture;
}

const LectureContent: React.FC<LectureContentProps> = ({ lecture }) => {
  const { language, t } = useLanguage();
  const [activeTab, setActiveTab] = useState<'overview' | 'materials' | 'quiz'>('overview');
  const [quizStarted, setQuizStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([]);
  const [showResults, setShowResults] = useState(false);

  const handleAnswerSelect = (answerIndex: number) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[currentQuestion] = answerIndex;
    setSelectedAnswers(newAnswers);
  };

  const handleNextQuestion = () => {
    if (lecture.quiz && currentQuestion < lecture.quiz.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      setShowResults(true);
    }
  };

  const calculateScore = () => {
    if (!lecture.quiz) return 0;
    let correct = 0;
    selectedAnswers.forEach((answer, index) => {
      if (answer === lecture.quiz!.questions[index].correctAnswer) {
        correct++;
      }
    });
    return Math.round((correct / lecture.quiz.questions.length) * 100);
  };

  const resetQuiz = () => {
    setQuizStarted(false);
    setCurrentQuestion(0);
    setSelectedAnswers([]);
    setShowResults(false);
  };

  const getMaterialIcon = (type: string) => {
    switch (type) {
      case 'video': return Video;
      case 'presentation': return FileText;
      case 'pdf': return BookOpen;
      default: return FileText;
    }
  };

  return (
    <div className="max-w-4xl mx-auto" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Lecture Header */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-green-600 to-teal-600 text-white p-8 rounded-xl shadow-lg">
          <div className={`flex items-center ${language === 'ar' ? 'space-x-4 space-x-reverse' : 'space-x-4'} mb-4`}>
            <Play className="h-8 w-8 text-green-100" />
            <div>
              <h1 className="text-3xl font-bold mb-2">
                {language === 'ar' ? lecture.arabicTitle : lecture.title}
              </h1>
              <p className="text-green-100 text-lg">
                {language === 'ar' ? lecture.title : lecture.arabicTitle}
              </p>
            </div>
          </div>
          
          <div className={`flex items-center ${language === 'ar' ? 'space-x-6 space-x-reverse' : 'space-x-6'} text-green-100 text-sm`}>
            <div className={`flex items-center ${language === 'ar' ? 'space-x-2 space-x-reverse' : 'space-x-2'}`}>
              <Play className="h-4 w-4" />
              <span>{t('duration')}: {lecture.duration} {t('minutes')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
        <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'overview'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {language === 'ar' ? 'نظرة عامة' : 'Overview'}
            </button>
            <button
              onClick={() => setActiveTab('materials')}
              className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'materials'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('lecture_materials')}
            </button>
            {lecture.quiz && (
              <button
                onClick={() => setActiveTab('quiz')}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'quiz'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {language === 'ar' ? 'الاختبار' : 'Quiz'}
              </button>
            )}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div>
              <div className="mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {language === 'ar' ? 'وصف المحاضرة' : 'Lecture Description'}
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {language === 'ar' ? lecture.arabicDescription : lecture.description}
                </p>
              </div>

              {lecture.videoUrl && (
                <div className="mb-6">
                  <button className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 space-x-reverse">
                    <Play className="h-5 w-5" />
                    <span>{t('watch_lecture')}</span>
                  </button>
                </div>
              )}

              {lecture.slides && lecture.slides.length > 0 && (
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-4">
                    {language === 'ar' ? 'شرائح المحاضرة' : 'Lecture Slides'}
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {lecture.slides.map((slide, index) => (
                      <div key={index} className="bg-gray-100 rounded-lg p-4 hover:bg-gray-200 transition-colors cursor-pointer">
                        <img 
                          src={slide} 
                          alt={`Slide ${index + 1}`}
                          className="w-full h-24 object-cover rounded mb-2"
                        />
                        <p className="text-sm text-gray-600 text-center">
                          {language === 'ar' ? `شريحة ${index + 1}` : `Slide ${index + 1}`}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'materials' && (
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                {t('lecture_materials')}
              </h3>
              {lecture.materials && lecture.materials.length > 0 ? (
                <div className="space-y-4">
                  {lecture.materials.map((material) => {
                    const IconComponent = getMaterialIcon(material.type);
                    return (
                      <div key={material.id} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                        <div className={`flex items-center ${language === 'ar' ? 'space-x-3 space-x-reverse' : 'space-x-3'}`}>
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <IconComponent className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {language === 'ar' ? material.arabicTitle : material.title}
                            </h4>
                            <p className="text-sm text-gray-500 capitalize">{material.type}</p>
                          </div>
                        </div>
                        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2 space-x-reverse">
                          <Download className="h-4 w-4" />
                          <span>{t('download_materials')}</span>
                        </button>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  {language === 'ar' ? 'لا توجد مواد متاحة لهذه المحاضرة' : 'No materials available for this lecture'}
                </p>
              )}
            </div>
          )}

          {activeTab === 'quiz' && lecture.quiz && (
            <div>
              {!quizStarted && !showResults && (
                <div className="text-center py-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {language === 'ar' ? 'اختبار المحاضرة' : 'Lecture Quiz'}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {language === 'ar' 
                      ? `يحتوي هذا الاختبار على ${lecture.quiz.questions.length} أسئلة`
                      : `This quiz contains ${lecture.quiz.questions.length} questions`
                    }
                  </p>
                  <button
                    onClick={() => setQuizStarted(true)}
                    className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                  >
                    {t('take_quiz')}
                  </button>
                </div>
              )}

              {quizStarted && !showResults && (
                <div>
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {language === 'ar' 
                          ? `السؤال ${currentQuestion + 1} من ${lecture.quiz.questions.length}`
                          : `Question ${currentQuestion + 1} of ${lecture.quiz.questions.length}`
                        }
                      </h3>
                      <div className="bg-gray-200 rounded-full h-2 w-32">
                        <div 
                          className="bg-green-600 h-2 rounded-full transition-all"
                          style={{ width: `${((currentQuestion + 1) / lecture.quiz.questions.length) * 100}%` }}
                        />
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-medium text-gray-900 mb-4">
                        {language === 'ar' 
                          ? lecture.quiz.questions[currentQuestion].arabicQuestion
                          : lecture.quiz.questions[currentQuestion].question
                        }
                      </h4>
                      
                      <div className="space-y-3">
                        {(language === 'ar' 
                          ? lecture.quiz.questions[currentQuestion].arabicOptions
                          : lecture.quiz.questions[currentQuestion].options
                        ).map((option, index) => (
                          <button
                            key={index}
                            onClick={() => handleAnswerSelect(index)}
                            className={`w-full text-left p-4 rounded-lg border transition-colors ${
                              selectedAnswers[currentQuestion] === index
                                ? 'border-green-500 bg-green-50 text-green-700'
                                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                            }`}
                          >
                            <span className="font-medium mr-3">
                              {String.fromCharCode(65 + index)}.
                            </span>
                            {option}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={handleNextQuestion}
                      disabled={selectedAnswers[currentQuestion] === undefined}
                      className="bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-medium transition-colors"
                    >
                      {currentQuestion === lecture.quiz.questions.length - 1
                        ? (language === 'ar' ? 'إنهاء الاختبار' : 'Finish Quiz')
                        : (language === 'ar' ? 'السؤال التالي' : 'Next Question')
                      }
                    </button>
                  </div>
                </div>
              )}

              {showResults && (
                <div className="text-center py-8">
                  <div className="mb-6">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
                      calculateScore() >= 70 ? 'bg-green-100' : 'bg-red-100'
                    }`}>
                      {calculateScore() >= 70 ? (
                        <CheckCircle className="h-8 w-8 text-green-600" />
                      ) : (
                        <XCircle className="h-8 w-8 text-red-600" />
                      )}
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {t('quiz_results')}
                    </h3>
                    <p className="text-3xl font-bold mb-2" style={{ color: calculateScore() >= 70 ? '#059669' : '#DC2626' }}>
                      {calculateScore()}%
                    </p>
                    <p className="text-gray-600">
                      {language === 'ar' 
                        ? `${selectedAnswers.filter((answer, index) => answer === lecture.quiz!.questions[index].correctAnswer).length} من ${lecture.quiz.questions.length} إجابات صحيحة`
                        : `${selectedAnswers.filter((answer, index) => answer === lecture.quiz!.questions[index].correctAnswer).length} out of ${lecture.quiz.questions.length} correct answers`
                      }
                    </p>
                  </div>
                  
                  <button
                    onClick={resetQuiz}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 space-x-reverse mx-auto"
                  >
                    <RotateCcw className="h-5 w-5" />
                    <span>{language === 'ar' ? 'إعادة الاختبار' : 'Retake Quiz'}</span>
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LectureContent;