<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bilingual Test - Medical Device Innovation Guide</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-heartbeat me-2"></i>
                <span data-i18n-key="app_title">Medical Device Innovation Guide</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html" data-i18n-key="nav_home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html" data-i18n-key="nav_dashboard">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html" data-i18n-key="nav_resources">Resources</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm" id="languageToggle" data-i18n-key="language_toggle" title="Switch language">
                            <i class="fas fa-globe me-2"></i>
                            <span class="current-lang">EN</span>
                            <span class="separator">|</span>
                            <span class="other-lang">ع</span>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5 pt-5">
        <h1>Bilingual Functionality Test</h1>
        <p class="lead">This page tests the bilingual functionality of the Medical Device Innovation Guide.</p>
        
        <div class="row g-4">
            <!-- Language Test -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-language me-2"></i>Language Switching Test</h5>
                    </div>
                    <div class="card-body">
                        <p>Current Language: <span id="currentLang" class="badge bg-primary">Loading...</span></p>
                        <p>Browser Language: <span id="browserLang" class="badge bg-info"></span></p>
                        
                        <h6>Test Elements:</h6>
                        <ul>
                            <li data-i18n-key="app_title">App Title</li>
                            <li data-i18n-key="nav_home">Home</li>
                            <li data-i18n-key="nav_dashboard">Dashboard</li>
                            <li data-i18n-key="start_learning_btn">Start Learning</li>
                            <li data-i18n-key="why_this_guide">Why This Guide?</li>
                        </ul>
                        
                        <button class="btn btn-primary" onclick="testLanguageSwitch()">
                            <i class="fas fa-sync me-2"></i>Test Language Switch
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Content Rendering Test -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-file-alt me-2"></i>Content Rendering Test</h5>
                    </div>
                    <div class="card-body">
                        <p>Test dynamic content loading from content store:</p>
                        
                        <div class="mb-3">
                            <label class="form-label">Select Stage:</label>
                            <select class="form-select" id="stageSelect">
                                <option value="stage_01_introduction">Stage 1: Introduction</option>
                                <option value="stage_02_landscape">Stage 2: Landscape</option>
                                <option value="stage_03_process">Stage 3: Process</option>
                                <option value="stage_04_opportunity">Stage 4: Opportunity</option>
                                <option value="stage_05_needs">Stage 5: Needs</option>
                            </select>
                        </div>
                        
                        <button class="btn btn-success" onclick="testContentRendering()">
                            <i class="fas fa-play me-2"></i>Render Content
                        </button>
                        
                        <div id="contentTest" class="mt-3">
                            <!-- Content will be rendered here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- RTL Test -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-align-right me-2"></i>RTL Layout Test</h5>
                    </div>
                    <div class="card-body">
                        <p>Test RTL layout and Arabic text rendering:</p>
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <h6>English (LTR)</h6>
                                <div class="p-3 border rounded">
                                    <p><strong>Medical Device Innovation Guide</strong></p>
                                    <p>An interactive academic journey from concept to market.</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary btn-sm">Start Learning</button>
                                        <button class="btn btn-outline-primary btn-sm">Resources</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Arabic (RTL)</h6>
                                <div class="p-3 border rounded" dir="rtl">
                                    <p><strong>دليل الابتكار في الأجهزة الطبية</strong></p>
                                    <p>رحلة أكاديمية تفاعلية من الفكرة إلى السوق.</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary btn-sm">ابدأ التعلم</button>
                                        <button class="btn btn-outline-primary btn-sm">الموارد</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-check me-2"></i>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">Run tests above to see results here.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Links -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-link me-2"></i>Quick Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2">
                            <a href="index.html" class="btn btn-outline-primary">Home Page</a>
                            <a href="dashboard.html" class="btn btn-outline-success">Dashboard</a>
                            <a href="stage-01-bilingual.html" class="btn btn-outline-info">Bilingual Stage 1</a>
                            <a href="resources.html" class="btn btn-outline-warning">Resources</a>
                            <a href="test.html" class="btn btn-outline-secondary">Original Test Page</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/i18n.js"></script>
    <script src="js/content-store.js"></script>
    <script src="js/ui-builder.js"></script>
    <script src="main.js"></script>
    
    <script>
        // Test functions
        function addTestResult(test, status, message) {
            const resultsDiv = document.getElementById('testResults');
            const statusClass = status === 'pass' ? 'text-success' : 'text-danger';
            const icon = status === 'pass' ? 'fa-check-circle' : 'fa-times-circle';
            
            resultsDiv.innerHTML += `
                <div class="test-result mb-2">
                    <i class="fas ${icon} ${statusClass} me-2"></i>
                    <strong>${test}:</strong> 
                    <span class="${statusClass}">${status.toUpperCase()}</span>
                    ${message ? ` - ${message}` : ''}
                </div>
            `;
        }
        
        function testLanguageSwitch() {
            try {
                if (typeof window.I18n !== 'undefined') {
                    const currentLang = window.I18n.getCurrentLanguage();
                    const newLang = currentLang === 'en' ? 'ar' : 'en';
                    
                    window.I18n.setLanguage(newLang);
                    
                    // Check if language changed
                    const updatedLang = window.I18n.getCurrentLanguage();
                    if (updatedLang === newLang) {
                        addTestResult('Language Switch', 'pass', `Switched from ${currentLang} to ${newLang}`);
                        updateCurrentLanguageDisplay();
                    } else {
                        addTestResult('Language Switch', 'fail', 'Language did not change');
                    }
                } else {
                    addTestResult('Language Switch', 'fail', 'I18n system not available');
                }
            } catch (error) {
                addTestResult('Language Switch', 'fail', error.message);
            }
        }
        
        function testContentRendering() {
            try {
                const stageKey = document.getElementById('stageSelect').value;
                const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'en';
                
                if (typeof window.UIBuilder !== 'undefined' && window.UIBuilder.renderGuideContent) {
                    // Create a test container
                    const testContainer = document.getElementById('contentTest');
                    testContainer.innerHTML = '<div class="accordion" id="testAccordion"></div>';
                    
                    window.UIBuilder.renderGuideContent(stageKey, currentLang, 'testAccordion');
                    
                    // Check if content was rendered
                    const accordion = document.getElementById('testAccordion');
                    if (accordion && accordion.children.length > 0) {
                        addTestResult('Content Rendering', 'pass', `Rendered ${accordion.children.length} sections for ${stageKey} in ${currentLang}`);
                    } else {
                        addTestResult('Content Rendering', 'fail', 'No content was rendered');
                    }
                } else {
                    addTestResult('Content Rendering', 'fail', 'UIBuilder not available');
                }
            } catch (error) {
                addTestResult('Content Rendering', 'fail', error.message);
            }
        }
        
        function updateCurrentLanguageDisplay() {
            const currentLang = window.I18n ? window.I18n.getCurrentLanguage() : 'unknown';
            document.getElementById('currentLang').textContent = currentLang.toUpperCase();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Display browser language
            const browserLang = navigator.language || navigator.userLanguage;
            document.getElementById('browserLang').textContent = browserLang;
            
            // Update current language display
            updateCurrentLanguageDisplay();
            
            // Listen for language changes
            window.addEventListener('languageChanged', function(event) {
                updateCurrentLanguageDisplay();
                addTestResult('Language Change Event', 'pass', `Language changed to ${event.detail.language}`);
            });
            
            // Auto-run basic tests
            setTimeout(() => {
                addTestResult('I18n System', window.I18n ? 'pass' : 'fail', window.I18n ? 'I18n system loaded' : 'I18n system not found');
                addTestResult('Content Store', window.ContentStore ? 'pass' : 'fail', window.ContentStore ? 'Content store loaded' : 'Content store not found');
                addTestResult('UI Builder', window.UIBuilder ? 'pass' : 'fail', window.UIBuilder ? 'UI Builder loaded' : 'UI Builder not found');
            }, 1000);
        });
    </script>
</body>
</html>
