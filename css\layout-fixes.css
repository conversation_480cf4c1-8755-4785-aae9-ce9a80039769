/**
 * Medical Device Innovation Guide - Layout Fixes
 * CSS fixes for overlapping and z-index issues
 */

/* Global Layout Fixes */
html {
    scroll-behavior: smooth;
}

body {
    padding-top: 60px !important; /* Account for fixed navigation */
    position: relative;
    overflow-x: hidden;
}

/* Navigation Z-Index Hierarchy */
.enhanced-navigation {
    z-index: 1050 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
}

.enhanced-nav-bar {
    z-index: 1051 !important;
    position: relative;
}

.search-panel,
.bookmarks-panel {
    z-index: 1049 !important;
    position: relative;
}

.breadcrumb-container {
    z-index: 1048 !important;
    position: relative;
    margin-top: 0 !important;
}

/* Main Content Areas */
.hero-section {
    margin-top: 0 !important;
    padding-top: 2rem !important;
    position: relative;
    z-index: 1 !important;
}

.showcase-header {
    margin-top: 0 !important;
    padding-top: 3rem !important;
    position: relative;
    z-index: 1 !important;
}

.main-content,
main {
    position: relative;
    z-index: 1 !important;
    margin-top: 0 !important;
}

.container,
.container-fluid {
    position: relative;
    z-index: 1 !important;
}

/* Section Spacing */
section {
    position: relative;
    z-index: 1 !important;
}

section:first-of-type {
    margin-top: 0 !important;
}

/* Card and Component Fixes */
.card,
.integration-card,
.feature-card,
.workspace-section,
.exercise-card {
    position: relative;
    z-index: 1 !important;
}

/* Modal and Overlay Z-Index */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1054 !important;
}

.dropdown-menu {
    z-index: 1060 !important;
}

.tooltip {
    z-index: 1070 !important;
}

.popover {
    z-index: 1065 !important;
}

/* Floating Elements */
.floating-actions {
    z-index: 1030 !important;
    position: fixed !important;
}

.fab-container {
    position: relative;
    z-index: 1 !important;
}

.fab-menu {
    z-index: 2 !important;
    position: absolute;
}

/* Form Elements */
.form-control,
.form-select,
.btn,
.input-group {
    position: relative;
    z-index: 1 !important;
}

/* Accordion and Collapse */
.accordion {
    position: relative;
    z-index: 1 !important;
}

.accordion-item {
    position: relative;
    z-index: 1 !important;
}

.accordion-collapse {
    position: relative;
    z-index: 1 !important;
}

/* Table and Data Display */
.table-responsive {
    position: relative;
    z-index: 1 !important;
}

.progress {
    position: relative;
    z-index: 1 !important;
}

/* Workspace Tools Specific */
.workspace-column {
    position: relative;
    z-index: 1 !important;
    margin-top: 1rem;
}

.workspace-header {
    position: relative;
    z-index: 1 !important;
}

.assessment-matrix,
.swot-matrix,
.stakeholder-categories,
.priority-matrix {
    position: relative;
    z-index: 1 !important;
}

.brainstorming-area,
.test-designer,
.study-design,
.fmea-builder {
    position: relative;
    z-index: 1 !important;
}

/* Interactive Book Specific */
.bg-white {
    position: relative;
    z-index: 1 !important;
}

.rounded-lg {
    position: relative;
    z-index: 1 !important;
}

/* Mobile Responsive Fixes */
@media (max-width: 768px) {
    body {
        padding-top: 70px !important;
    }
    
    .hero-section {
        padding-top: 1rem !important;
    }
    
    .showcase-header {
        padding-top: 2rem !important;
    }
    
    .nav-menu {
        z-index: 1045 !important;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
    }
    
    .floating-actions {
        bottom: 1rem !important;
        right: 1rem !important;
    }
}

/* RTL Layout Fixes */
html[dir="rtl"] body {
    padding-top: 60px !important;
}

html[dir="rtl"] .floating-actions {
    right: auto !important;
    left: 2rem !important;
}

html[dir="rtl"] .fab-menu {
    right: auto !important;
    left: 0 !important;
}

/* Specific Page Fixes */

/* Index Page */
.min-vh-100 {
    min-height: calc(100vh - 60px) !important;
}

/* Dashboard Page */
.sidebar {
    position: relative;
    z-index: 1 !important;
}

.dashboard-content {
    position: relative;
    z-index: 1 !important;
}

/* Interactive Book Page */
.mx-auto {
    position: relative;
    z-index: 1 !important;
}

/* Stage Pages */
.stage-content {
    position: relative;
    z-index: 1 !important;
}

.stage-navigation {
    position: relative;
    z-index: 1 !important;
}

/* Advanced Demo Page */
.demo-section {
    position: relative;
    z-index: 1 !important;
}

.stats-grid {
    position: relative;
    z-index: 1 !important;
}

.stat-card {
    position: relative;
    z-index: 1 !important;
}

/* Content Manager Page */
.admin-header {
    position: relative;
    z-index: 1 !important;
}

.content-editor {
    position: relative;
    z-index: 1 !important;
}

/* Test Page */
.test-section {
    position: relative;
    z-index: 1 !important;
}

/* Ensure Proper Stacking Context */
.stacking-context {
    position: relative;
    z-index: 1 !important;
}

/* Fix for Tailwind CSS conflicts */
.bg-gradient-to-br {
    position: relative;
    z-index: 1 !important;
}

.shadow-lg,
.shadow-md {
    position: relative;
    z-index: 1 !important;
}

/* Animation and Transition Fixes */
.transition-all {
    position: relative;
    z-index: 1 !important;
}

/* Utility Classes for Manual Fixes */
.fix-z-index {
    position: relative !important;
    z-index: 1 !important;
}

.fix-z-index-high {
    position: relative !important;
    z-index: 10 !important;
}

.fix-no-overlap {
    position: relative !important;
    z-index: 1 !important;
    margin-top: 0 !important;
}

.fix-spacing-top {
    margin-top: 1rem !important;
}

.fix-spacing-bottom {
    margin-bottom: 1rem !important;
}

/* Print Media Fixes */
@media print {
    body {
        padding-top: 0 !important;
    }
    
    .enhanced-navigation,
    .floating-actions {
        display: none !important;
    }
}
