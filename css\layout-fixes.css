/**
 * Medical Device Innovation Guide - Layout Fixes
 * CSS fixes for overlapping and z-index issues
 */

/* Global Layout Fixes */
html {
    scroll-behavior: smooth;
}

body {
    padding-top: 0 !important; /* Remove default padding */
    position: relative;
    overflow-x: hidden;
    margin: 0;
}

/* Ensure no content overlaps with navigation */
.hero-section,
.showcase-header,
section:first-of-type,
main:first-child {
    margin-top: 60px !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Navigation Z-Index Hierarchy */
.enhanced-navigation {
    z-index: 1050 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
}

.enhanced-nav-bar {
    z-index: 1051 !important;
    position: relative;
}

.search-panel,
.bookmarks-panel {
    z-index: 1049 !important;
    position: relative;
}

.breadcrumb-container {
    z-index: 1048 !important;
    position: relative;
    margin-top: 0 !important;
}

/* Main Content Areas */
.hero-section {
    margin-top: 0 !important;
    padding-top: 2rem !important;
    position: relative;
    z-index: 1 !important;
}

.showcase-header {
    margin-top: 0 !important;
    padding-top: 3rem !important;
    position: relative;
    z-index: 1 !important;
}

.main-content,
main {
    position: relative;
    z-index: 1 !important;
    margin-top: 0 !important;
}

.container,
.container-fluid {
    position: relative;
    z-index: 1 !important;
}

/* Section Spacing */
section {
    position: relative;
    z-index: 1 !important;
}

section:first-of-type {
    margin-top: 0 !important;
}

/* Card and Component Fixes */
.card,
.integration-card,
.feature-card,
.workspace-section,
.exercise-card {
    position: relative;
    z-index: 1 !important;
}

/* Modal and Overlay Z-Index */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1054 !important;
}

.dropdown-menu {
    z-index: 1060 !important;
}

.tooltip {
    z-index: 1070 !important;
}

.popover {
    z-index: 1065 !important;
}

/* Floating Elements */
.floating-actions {
    z-index: 1030 !important;
    position: fixed !important;
}

.fab-container {
    position: relative;
    z-index: 1 !important;
}

.fab-menu {
    z-index: 2 !important;
    position: absolute;
}

/* Form Elements */
.form-control,
.form-select,
.btn,
.input-group {
    position: relative;
    z-index: 1 !important;
}

/* Accordion and Collapse */
.accordion {
    position: relative;
    z-index: 1 !important;
}

.accordion-item {
    position: relative;
    z-index: 1 !important;
}

.accordion-collapse {
    position: relative;
    z-index: 1 !important;
}

/* Table and Data Display */
.table-responsive {
    position: relative;
    z-index: 1 !important;
}

.progress {
    position: relative;
    z-index: 1 !important;
}

/* Workspace Tools Specific */
.workspace-column {
    position: relative;
    z-index: 1 !important;
    margin-top: 1rem;
}

.workspace-header {
    position: relative;
    z-index: 1 !important;
}

.assessment-matrix,
.swot-matrix,
.stakeholder-categories,
.priority-matrix {
    position: relative;
    z-index: 1 !important;
}

.brainstorming-area,
.test-designer,
.study-design,
.fmea-builder {
    position: relative;
    z-index: 1 !important;
}

/* Interactive Book Specific */
.bg-white {
    position: relative;
    z-index: 1 !important;
}

.rounded-lg {
    position: relative;
    z-index: 1 !important;
}

/* Mobile Responsive Fixes */
@media (max-width: 768px) {
    body {
        padding-top: 70px !important;
    }
    
    .hero-section {
        padding-top: 1rem !important;
    }
    
    .showcase-header {
        padding-top: 2rem !important;
    }
    
    .nav-menu {
        z-index: 1045 !important;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
    }
    
    .floating-actions {
        bottom: 1rem !important;
        right: 1rem !important;
    }
}

/* RTL Layout Fixes */
html[dir="rtl"] body {
    padding-top: 60px !important;
}

html[dir="rtl"] .floating-actions {
    right: auto !important;
    left: 2rem !important;
}

html[dir="rtl"] .fab-menu {
    right: auto !important;
    left: 0 !important;
}

/* Specific Page Fixes */

/* Index Page */
.min-vh-100 {
    min-height: calc(100vh - 60px) !important;
}

/* Dashboard Page */
.sidebar {
    position: relative;
    z-index: 1 !important;
}

.dashboard-content {
    position: relative;
    z-index: 1 !important;
}

/* Interactive Book Page */
.mx-auto {
    position: relative;
    z-index: 1 !important;
}

/* Stage Pages */
.stage-content {
    position: relative;
    z-index: 1 !important;
}

.stage-navigation {
    position: relative;
    z-index: 1 !important;
}

/* Advanced Demo Page */
.demo-section {
    position: relative;
    z-index: 1 !important;
}

.stats-grid {
    position: relative;
    z-index: 1 !important;
}

.stat-card {
    position: relative;
    z-index: 1 !important;
}

/* Content Manager Page */
.admin-header {
    position: relative;
    z-index: 1 !important;
}

.content-editor {
    position: relative;
    z-index: 1 !important;
}

/* Test Page */
.test-section {
    position: relative;
    z-index: 1 !important;
}

/* Ensure Proper Stacking Context */
.stacking-context {
    position: relative;
    z-index: 1 !important;
}

/* Fix for Tailwind CSS conflicts */
.bg-gradient-to-br {
    position: relative;
    z-index: 1 !important;
}

.shadow-lg,
.shadow-md {
    position: relative;
    z-index: 1 !important;
}

/* Animation and Transition Fixes */
.transition-all {
    position: relative;
    z-index: 1 !important;
}

/* Utility Classes for Manual Fixes */
.fix-z-index {
    position: relative !important;
    z-index: 1 !important;
}

.fix-z-index-high {
    position: relative !important;
    z-index: 10 !important;
}

.fix-no-overlap {
    position: relative !important;
    z-index: 1 !important;
    margin-top: 0 !important;
}

.fix-spacing-top {
    margin-top: 1rem !important;
}

.fix-spacing-bottom {
    margin-bottom: 1rem !important;
}

/* Print Media Fixes */
@media print {
    body {
        padding-top: 0 !important;
    }

    .enhanced-navigation,
    .floating-actions {
        display: none !important;
    }
}

/* Interactive Home Page Styles */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    margin-top: 60px !important;
    padding: 4rem 0 !important;
    position: relative !important;
    z-index: 1 !important;
}

.hero-content {
    animation: fadeInUp 1s ease-out;
}

.hero-visual {
    animation: fadeInRight 1s ease-out 0.3s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hero Stats */
.hero-stats .stat-item {
    padding: 1rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.hero-stats .stat-item:hover {
    transform: translateY(-5px);
}

/* Interactive Buttons */
.interactive-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.interactive-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.interactive-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.interactive-btn:hover::before {
    left: 100%;
}

/* Innovation Diagram */
.innovation-diagram {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto;
}

.diagram-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.diagram-orbit {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.orbit-1 {
    width: 120px;
    height: 120px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.orbit-2 {
    width: 180px;
    height: 180px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 30s;
}

.orbit-3 {
    width: 240px;
    height: 240px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 40s;
}

.orbit-item {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: counter-rotate 20s linear infinite reverse;
}

.orbit-2 .orbit-item {
    animation-duration: 30s;
}

.orbit-3 .orbit-item {
    animation-duration: 40s;
}

.orbit-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.orbit-1 .orbit-item {
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
}

.orbit-2 .orbit-item {
    top: -30px;
    right: -30px;
}

.orbit-3 .orbit-item {
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes counter-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(-360deg); }
}

/* Stage Group Cards */
.stage-group-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.stage-group-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.stage-group-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stage-group-card:hover::before {
    transform: scaleX(1);
}

.stage-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.stage-group-card:hover .stage-icon {
    transform: scale(1.1) rotate(5deg);
}

.stage-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.stage-progress .progress {
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    margin-bottom: 0.5rem;
}

.stage-progress .progress-bar {
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.5s ease;
}

.stage-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.stage-actions .btn {
    flex: 1;
    transition: all 0.3s ease;
}

/* Timeline Styles */
.stages-timeline {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 2rem;
    margin-top: 2rem;
}

.timeline-container {
    position: relative;
    max-width: 100%;
    overflow-x: auto;
    padding: 2rem 0;
}

.timeline-line {
    position: absolute;
    top: 50%;
    left: 5%;
    right: 5%;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
    transform: translateY(-50%);
}

.timeline-stages {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    min-width: 800px;
    padding: 0 5%;
}

.timeline-stage {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.stage-dot {
    width: 60px;
    height: 60px;
    background: white;
    border: 4px solid #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.timeline-stage:hover .stage-dot {
    background: #667eea;
    color: white;
    transform: scale(1.2);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.workspace-stage .stage-dot {
    border-color: #28a745;
    color: #28a745;
}

.workspace-stage:hover .stage-dot {
    background: #28a745;
    color: white;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.stage-tooltip {
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    white-space: nowrap;
    z-index: 10;
}

.timeline-stage:hover .stage-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-10px);
}

.workspace-badge {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-top: 0.5rem;
    display: inline-block;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0 !important;
    }

    .innovation-diagram {
        width: 250px;
        height: 250px;
    }

    .orbit-1 { width: 100px; height: 100px; }
    .orbit-2 { width: 150px; height: 150px; }
    .orbit-3 { width: 200px; height: 200px; }

    .orbit-item {
        width: 50px;
        height: 50px;
        font-size: 0.7rem;
    }

    .stage-group-card {
        padding: 1.5rem;
    }

    .stage-icon {
        width: 60px;
        height: 60px;
    }

    .timeline-container {
        padding: 1rem 0;
    }

    .timeline-stages {
        min-width: 600px;
    }

    .stage-dot {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    .stage-tooltip {
        font-size: 0.875rem;
        padding: 0.75rem;
    }
}

/* RTL Support */
html[dir="rtl"] .hero-visual {
    animation: fadeInLeft 1s ease-out 0.3s both;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

html[dir="rtl"] .timeline-line {
    left: 5%;
    right: 5%;
}

html[dir="rtl"] .stage-tooltip {
    text-align: right;
}
