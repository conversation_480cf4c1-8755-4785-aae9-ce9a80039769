/**
 * Medical Device Innovation Guide - Advanced Internationalization Features
 * Enhanced i18n with number formatting, date localization, and cultural adaptations
 */

/**
 * Advanced i18n utilities for cultural adaptations
 */
const AdvancedI18n = {
    
    /**
     * Format numbers according to locale
     * @param {number} number - Number to format
     * @param {string} lang - Language code
     * @param {Object} options - Formatting options
     * @returns {string} Formatted number
     */
    formatNumber: function(number, lang = 'en', options = {}) {
        const locales = {
            'en': 'en-US',
            'ar': 'ar-SA'
        };
        
        try {
            return new Intl.NumberFormat(locales[lang] || 'en-US', options).format(number);
        } catch (error) {
            console.warn('Number formatting failed:', error);
            return number.toString();
        }
    },
    
    /**
     * Format currency according to locale
     * @param {number} amount - Amount to format
     * @param {string} lang - Language code
     * @param {string} currency - Currency code (USD, SAR, etc.)
     * @returns {string} Formatted currency
     */
    formatCurrency: function(amount, lang = 'en', currency = 'USD') {
        const currencyMap = {
            'en': 'USD',
            'ar': 'SAR'
        };
        
        const actualCurrency = currency || currencyMap[lang] || 'USD';
        
        return this.formatNumber(amount, lang, {
            style: 'currency',
            currency: actualCurrency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
    },
    
    /**
     * Format dates according to locale
     * @param {Date} date - Date to format
     * @param {string} lang - Language code
     * @param {Object} options - Formatting options
     * @returns {string} Formatted date
     */
    formatDate: function(date, lang = 'en', options = {}) {
        const locales = {
            'en': 'en-US',
            'ar': 'ar-SA'
        };
        
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        const formatOptions = { ...defaultOptions, ...options };
        
        try {
            return new Intl.DateTimeFormat(locales[lang] || 'en-US', formatOptions).format(date);
        } catch (error) {
            console.warn('Date formatting failed:', error);
            return date.toLocaleDateString();
        }
    },
    
    /**
     * Format relative time (e.g., "2 hours ago")
     * @param {Date} date - Date to format
     * @param {string} lang - Language code
     * @returns {string} Relative time string
     */
    formatRelativeTime: function(date, lang = 'en') {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        const timeUnits = {
            en: {
                second: 'second',
                minute: 'minute',
                hour: 'hour',
                day: 'day',
                week: 'week',
                month: 'month',
                year: 'year'
            },
            ar: {
                second: 'ثانية',
                minute: 'دقيقة',
                hour: 'ساعة',
                day: 'يوم',
                week: 'أسبوع',
                month: 'شهر',
                year: 'سنة'
            }
        };
        
        const units = timeUnits[lang] || timeUnits.en;
        
        if (diffInSeconds < 60) {
            return lang === 'ar' ? 'الآن' : 'just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return lang === 'ar' ? 
                `منذ ${minutes} ${units.minute}${minutes > 1 ? (minutes > 10 ? '' : 'ين') : ''}` :
                `${minutes} ${units.minute}${minutes > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return lang === 'ar' ? 
                `منذ ${hours} ${units.hour}${hours > 1 ? (hours > 10 ? '' : 'ين') : ''}` :
                `${hours} ${units.hour}${hours > 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return lang === 'ar' ? 
                `منذ ${days} ${units.day}${days > 1 ? (days > 10 ? '' : 'ين') : ''}` :
                `${days} ${units.day}${days > 1 ? 's' : ''} ago`;
        }
    },
    
    /**
     * Get culturally appropriate greetings
     * @param {string} lang - Language code
     * @returns {Object} Greeting messages
     */
    getGreetings: function(lang = 'en') {
        const greetings = {
            en: {
                morning: 'Good morning',
                afternoon: 'Good afternoon',
                evening: 'Good evening',
                welcome: 'Welcome',
                welcomeBack: 'Welcome back'
            },
            ar: {
                morning: 'صباح الخير',
                afternoon: 'مساء الخير',
                evening: 'مساء الخير',
                welcome: 'أهلاً وسهلاً',
                welcomeBack: 'أهلاً بعودتك'
            }
        };
        
        return greetings[lang] || greetings.en;
    },
    
    /**
     * Get time-based greeting
     * @param {string} lang - Language code
     * @returns {string} Appropriate greeting
     */
    getTimeBasedGreeting: function(lang = 'en') {
        const hour = new Date().getHours();
        const greetings = this.getGreetings(lang);
        
        if (hour < 12) {
            return greetings.morning;
        } else if (hour < 17) {
            return greetings.afternoon;
        } else {
            return greetings.evening;
        }
    },
    
    /**
     * Format percentages with cultural considerations
     * @param {number} value - Percentage value (0-100)
     * @param {string} lang - Language code
     * @returns {string} Formatted percentage
     */
    formatPercentage: function(value, lang = 'en') {
        const formatted = this.formatNumber(value, lang, {
            style: 'percent',
            minimumFractionDigits: 0,
            maximumFractionDigits: 1
        });
        
        // Arabic uses different percentage symbol positioning
        if (lang === 'ar') {
            return formatted.replace('%', '٪');
        }
        
        return formatted;
    },
    
    /**
     * Get culturally appropriate success/error messages
     * @param {string} type - Message type ('success', 'error', 'warning', 'info')
     * @param {string} lang - Language code
     * @returns {Object} Message templates
     */
    getStatusMessages: function(type, lang = 'en') {
        const messages = {
            en: {
                success: {
                    save: 'Saved successfully!',
                    update: 'Updated successfully!',
                    delete: 'Deleted successfully!',
                    complete: 'Completed successfully!'
                },
                error: {
                    save: 'Failed to save. Please try again.',
                    update: 'Failed to update. Please try again.',
                    delete: 'Failed to delete. Please try again.',
                    network: 'Network error. Please check your connection.'
                },
                warning: {
                    unsaved: 'You have unsaved changes.',
                    required: 'Please fill in all required fields.',
                    confirm: 'Are you sure you want to continue?'
                },
                info: {
                    loading: 'Loading...',
                    processing: 'Processing...',
                    saving: 'Saving...'
                }
            },
            ar: {
                success: {
                    save: 'تم الحفظ بنجاح!',
                    update: 'تم التحديث بنجاح!',
                    delete: 'تم الحذف بنجاح!',
                    complete: 'تم الإكمال بنجاح!'
                },
                error: {
                    save: 'فشل في الحفظ. يرجى المحاولة مرة أخرى.',
                    update: 'فشل في التحديث. يرجى المحاولة مرة أخرى.',
                    delete: 'فشل في الحذف. يرجى المحاولة مرة أخرى.',
                    network: 'خطأ في الشبكة. يرجى التحقق من الاتصال.'
                },
                warning: {
                    unsaved: 'لديك تغييرات غير محفوظة.',
                    required: 'يرجى ملء جميع الحقول المطلوبة.',
                    confirm: 'هل أنت متأكد من أنك تريد المتابعة؟'
                },
                info: {
                    loading: 'جاري التحميل...',
                    processing: 'جاري المعالجة...',
                    saving: 'جاري الحفظ...'
                }
            }
        };
        
        return messages[lang]?.[type] || messages.en[type];
    },
    
    /**
     * Update DOM elements with advanced formatting
     * @param {string} lang - Language code
     */
    updateAdvancedElements: function(lang = 'en') {
        // Update numbers
        document.querySelectorAll('[data-i18n-number]').forEach(element => {
            const value = parseFloat(element.getAttribute('data-i18n-number'));
            if (!isNaN(value)) {
                element.textContent = this.formatNumber(value, lang);
            }
        });
        
        // Update currencies
        document.querySelectorAll('[data-i18n-currency]').forEach(element => {
            const value = parseFloat(element.getAttribute('data-i18n-currency'));
            const currency = element.getAttribute('data-currency') || (lang === 'ar' ? 'SAR' : 'USD');
            if (!isNaN(value)) {
                element.textContent = this.formatCurrency(value, lang, currency);
            }
        });
        
        // Update dates
        document.querySelectorAll('[data-i18n-date]').forEach(element => {
            const dateStr = element.getAttribute('data-i18n-date');
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                element.textContent = this.formatDate(date, lang);
            }
        });
        
        // Update relative times
        document.querySelectorAll('[data-i18n-relative-time]').forEach(element => {
            const dateStr = element.getAttribute('data-i18n-relative-time');
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                element.textContent = this.formatRelativeTime(date, lang);
            }
        });
        
        // Update percentages
        document.querySelectorAll('[data-i18n-percentage]').forEach(element => {
            const value = parseFloat(element.getAttribute('data-i18n-percentage'));
            if (!isNaN(value)) {
                element.textContent = this.formatPercentage(value / 100, lang);
            }
        });
    },
    
    /**
     * Get market size labels in appropriate language
     * @param {string} size - Size category ('small', 'medium', 'large', 'mega')
     * @param {string} lang - Language code
     * @returns {string} Localized size label
     */
    getMarketSizeLabel: function(size, lang = 'en') {
        const labels = {
            en: {
                small: 'Small Market (<$100M)',
                medium: 'Medium Market ($100M-$1B)',
                large: 'Large Market ($1B-$10B)',
                mega: 'Mega Market ($10B+)'
            },
            ar: {
                small: 'سوق صغير (أقل من 100 مليون دولار)',
                medium: 'سوق متوسط (100 مليون - 1 مليار دولار)',
                large: 'سوق كبير (1-10 مليار دولار)',
                mega: 'سوق ضخم (أكثر من 10 مليار دولار)'
            }
        };
        
        return labels[lang]?.[size] || labels.en[size] || size;
    }
};

// Extend the main I18n system with advanced features
if (window.I18n) {
    window.I18n.Advanced = AdvancedI18n;
    
    // Listen for language changes and update advanced elements
    window.addEventListener('languageChanged', function(event) {
        AdvancedI18n.updateAdvancedElements(event.detail.language);
    });
}

// Export for standalone use
window.AdvancedI18n = AdvancedI18n;
