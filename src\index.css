@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Arabic text */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  font-family: 'Noto Sans Arabic', 'Inter', system-ui, -apple-system, sans-serif;
}

/* Custom prose styles for Arabic content */
.prose {
  @apply text-gray-800;
}

.prose[dir="rtl"] {
  text-align: right;
}

.prose[dir="ltr"] {
  text-align: left;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  @apply text-gray-900 font-bold;
  line-height: 1.3;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  @apply text-3xl;
}

.prose h2 {
  @apply text-2xl;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.prose h3 {
  @apply text-xl;
  color: #1f2937;
}

.prose h4 {
  @apply text-lg;
  color: #374151;
}

.prose p {
  @apply text-gray-700 leading-relaxed;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.prose ul,
.prose ol {
  @apply mb-6;
  margin-inline-start: 1.5rem;
}

.prose[dir="rtl"] ul,
.prose[dir="rtl"] ol {
  margin-inline-start: 1.5rem;
  margin-inline-end: 0;
}

.prose[dir="ltr"] ul,
.prose[dir="ltr"] ol {
  margin-inline-start: 1.5rem;
  margin-inline-end: 0;
}

.prose li {
  @apply mb-2 text-gray-700;
}

.prose ul li {
  @apply relative;
}

.prose[dir="rtl"] ul li::before {
  content: "•";
  @apply absolute text-blue-600 font-bold text-lg;
  right: -1.5rem;
  top: 0;
}

.prose[dir="ltr"] ul li::before {
  content: "•";
  @apply absolute text-blue-600 font-bold text-lg;
  left: -1.5rem;
  top: 0;
}

.prose ol li {
  @apply relative;
}

.prose strong {
  @apply font-semibold text-gray-900;
}

.prose em {
  @apply italic text-gray-800;
}

.prose blockquote {
  @apply py-2 my-6 bg-blue-50 text-blue-900;
  border-width: 4px;
  border-style: solid;
  border-color: #3b82f6;
  padding-inline-start: 1rem;
}

.prose[dir="rtl"] blockquote {
  border-right-width: 4px;
  border-left-width: 0;
}

.prose[dir="ltr"] blockquote {
  border-left-width: 4px;
  border-right-width: 0;
}

.prose code {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm;
}

.prose pre {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
  direction: ltr;
}

.prose table {
  @apply w-full border-collapse border border-gray-300 my-6;
}

.prose th,
.prose td {
  @apply border border-gray-300 px-4 py-2;
}

.prose[dir="rtl"] th,
.prose[dir="rtl"] td {
  text-align: right;
}

.prose[dir="ltr"] th,
.prose[dir="ltr"] td {
  text-align: left;
}

.prose th {
  @apply bg-gray-50 font-semibold text-gray-900;
}

.prose a {
  @apply text-blue-600 hover:text-blue-800 underline;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth transitions */
.transition-all {
  transition: all 0.2s ease-in-out;
}

/* Focus states */
button:focus,
input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .prose {
    font-size: 1rem;
  }
  
  .prose h1 {
    @apply text-2xl;
  }
  
  .prose h2 {
    @apply text-xl;
  }
  
  .prose h3 {
    @apply text-lg;
  }
}

/* Print styles */
@media print {
  .prose {
    color: black;
    font-size: 12pt;
    line-height: 1.5;
  }
  
  .prose h1,
  .prose h2,
  .prose h3 {
    page-break-after: avoid;
  }
  
  .prose p,
  .prose li {
    page-break-inside: avoid;
  }
}

/* RTL/LTR specific adjustments */
[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="ltr"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
}

/* Language-specific font optimizations */
[lang="ar"] {
  font-family: 'Noto Sans Arabic', system-ui, -apple-system, sans-serif;
}

[lang="en"] {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}