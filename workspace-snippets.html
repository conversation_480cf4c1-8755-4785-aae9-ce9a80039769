<!-- STAGE 3: Innovation Process - Workspace Column -->
<div class="col-lg-6 workspace-column" id="stage-03-workspace">
    <div class="workspace-header mb-4">
        <h2 data-i18n-key="nav_workspace">Workspace</h2>
        <p class="text-muted">Process mapping and innovation framework</p>
        
        <div class="stage-navigation">
            <a href="stage-02-bilingual.html" class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
            </a>
            <a href="stage-04-bilingual.html" class="btn btn-primary">
                <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>
    
    <div class="workspace-content">
        <!-- Learning Objectives -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="learning_objectives">Learning Objectives</h4>
            <div class="objectives-list">
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Map the complete innovation process from idea to market</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Understand key decision gates and milestones</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Identify critical success factors at each stage</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Create a personalized innovation roadmap</span>
                </div>
            </div>
        </div>

        <!-- Innovation Process Mapper -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="process_mapper">Innovation Process Mapper</h4>
            <div class="exercise-card">
                <h5>Build Your Innovation Roadmap</h5>
                <p>Create a customized process map for your specific innovation:</p>
                
                <div class="process-builder">
                    <div class="mb-3">
                        <label class="form-label">Innovation Type:</label>
                        <select class="form-select" id="innovationType">
                            <option value="">Select type...</option>
                            <option value="diagnostic">Diagnostic Device</option>
                            <option value="therapeutic">Therapeutic Device</option>
                            <option value="monitoring">Monitoring Device</option>
                            <option value="surgical">Surgical Instrument</option>
                            <option value="digital">Digital Health Solution</option>
                            <option value="implant">Implantable Device</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Risk Classification:</label>
                        <select class="form-select" id="riskClass">
                            <option value="">Select class...</option>
                            <option value="class1">Class I (Low Risk)</option>
                            <option value="class2a">Class IIa (Medium Risk)</option>
                            <option value="class2b">Class IIb (Medium-High Risk)</option>
                            <option value="class3">Class III (High Risk)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Target Market:</label>
                        <select class="form-select" id="targetMarket">
                            <option value="">Select market...</option>
                            <option value="us">United States</option>
                            <option value="eu">European Union</option>
                            <option value="global">Global</option>
                            <option value="emerging">Emerging Markets</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Development Timeline:</label>
                        <select class="form-select" id="timeline">
                            <option value="">Select timeline...</option>
                            <option value="fast">Fast Track (1-2 years)</option>
                            <option value="standard">Standard (3-5 years)</option>
                            <option value="extended">Extended (5+ years)</option>
                        </select>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="generateProcessMap(3)">
                    <i class="fas fa-map me-2"></i>Generate Process Map
                </button>
                
                <div id="processMapResult" class="mt-3" style="display: none;">
                    <h6>Your Innovation Process Map:</h6>
                    <div id="processSteps" class="process-visualization"></div>
                </div>
            </div>
        </div>

        <!-- Stage Gate Framework -->
        <div class="workspace-section mb-4">
            <h5>Stage-Gate Framework</h5>
            <div class="stage-gates">
                <div class="gate-item" data-gate="discovery">
                    <div class="gate-header">
                        <h6><i class="fas fa-lightbulb me-2"></i>Discovery</h6>
                        <span class="gate-status badge bg-secondary">Not Started</span>
                    </div>
                    <div class="gate-criteria">
                        <small>Key Criteria: Market research, needs assessment, initial feasibility</small>
                    </div>
                </div>
                
                <div class="gate-item" data-gate="concept">
                    <div class="gate-header">
                        <h6><i class="fas fa-drafting-compass me-2"></i>Concept</h6>
                        <span class="gate-status badge bg-secondary">Not Started</span>
                    </div>
                    <div class="gate-criteria">
                        <small>Key Criteria: Technical feasibility, business case, IP strategy</small>
                    </div>
                </div>
                
                <div class="gate-item" data-gate="development">
                    <div class="gate-header">
                        <h6><i class="fas fa-cogs me-2"></i>Development</h6>
                        <span class="gate-status badge bg-secondary">Not Started</span>
                    </div>
                    <div class="gate-criteria">
                        <small>Key Criteria: Prototype validation, regulatory pathway, manufacturing plan</small>
                    </div>
                </div>
                
                <div class="gate-item" data-gate="testing">
                    <div class="gate-header">
                        <h6><i class="fas fa-flask me-2"></i>Testing</h6>
                        <span class="gate-status badge bg-secondary">Not Started</span>
                    </div>
                    <div class="gate-criteria">
                        <small>Key Criteria: Clinical validation, regulatory approval, quality systems</small>
                    </div>
                </div>
                
                <div class="gate-item" data-gate="launch">
                    <div class="gate-header">
                        <h6><i class="fas fa-rocket me-2"></i>Launch</h6>
                        <span class="gate-status badge bg-secondary">Not Started</span>
                    </div>
                    <div class="gate-criteria">
                        <small>Key Criteria: Market readiness, commercial strategy, post-market surveillance</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="workspace-section">
            <h5 data-i18n-key="your_progress">Your Progress</h5>
            <div class="progress mb-2">
                <div class="progress-bar" style="width: 15%"></div>
            </div>
            <small class="text-muted">Stage 3 of 20 (15%)</small>
        </div>
    </div>
</div>

<!-- STAGE 4: Understanding Opportunity - Workspace Column -->
<div class="col-lg-6 workspace-column" id="stage-04-workspace">
    <div class="workspace-header mb-4">
        <h2 data-i18n-key="nav_workspace">Workspace</h2>
        <p class="text-muted">Opportunity assessment and validation</p>
        
        <div class="stage-navigation">
            <a href="stage-03-bilingual.html" class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
            </a>
            <a href="stage-05-bilingual.html" class="btn btn-primary">
                <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>
    
    <div class="workspace-content">
        <!-- Learning Objectives -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="learning_objectives">Learning Objectives</h4>
            <div class="objectives-list">
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Evaluate market opportunities systematically</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Assess technical and commercial feasibility</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Identify key success factors and risks</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Create a compelling opportunity statement</span>
                </div>
            </div>
        </div>

        <!-- Opportunity Assessment Matrix -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="opportunity_assessment">Opportunity Assessment</h4>
            <div class="exercise-card">
                <h5>Multi-Criteria Opportunity Evaluation</h5>
                <p>Rate each factor on a scale of 1-5 (1=Poor, 5=Excellent):</p>
                
                <div class="assessment-matrix">
                    <div class="assessment-category">
                        <h6><i class="fas fa-chart-line me-2"></i>Market Factors</h6>
                        <div class="factor-rating">
                            <label>Market Size:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="marketSize" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                        <div class="factor-rating">
                            <label>Growth Rate:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="growthRate" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                        <div class="factor-rating">
                            <label>Unmet Need:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="unmetNeed" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="assessment-category">
                        <h6><i class="fas fa-cogs me-2"></i>Technical Factors</h6>
                        <div class="factor-rating">
                            <label>Technical Feasibility:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="techFeasibility" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                        <div class="factor-rating">
                            <label>Development Risk:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="devRisk" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                        <div class="factor-rating">
                            <label>IP Potential:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="ipPotential" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="assessment-category">
                        <h6><i class="fas fa-dollar-sign me-2"></i>Commercial Factors</h6>
                        <div class="factor-rating">
                            <label>Revenue Potential:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="revenuePotential" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                        <div class="factor-rating">
                            <label>Competitive Advantage:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="compAdvantage" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                        <div class="factor-rating">
                            <label>Regulatory Pathway:</label>
                            <div class="rating-scale">
                                <input type="range" class="form-range" min="1" max="5" value="3" id="regPathway" onchange="updateOpportunityScore()">
                                <span class="rating-value">3</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="opportunity-score mt-3">
                    <h6>Overall Opportunity Score: <span id="totalScore" class="text-primary">27/45</span></h6>
                    <div class="progress">
                        <div class="progress-bar" id="scoreProgress" style="width: 60%"></div>
                    </div>
                    <div id="scoreInterpretation" class="mt-2">
                        <small class="text-muted">Moderate opportunity with potential for improvement</small>
                    </div>
                </div>
                
                <button class="btn btn-primary mt-3" onclick="saveOpportunityAssessment(4)">
                    <i class="fas fa-save me-2"></i>Save Assessment
                </button>
            </div>
        </div>

        <!-- SWOT Analysis -->
        <div class="workspace-section mb-4">
            <h5>SWOT Analysis</h5>
            <div class="swot-matrix">
                <div class="swot-quadrant strengths">
                    <h6><i class="fas fa-plus-circle text-success me-2"></i>Strengths</h6>
                    <textarea class="form-control" rows="3" placeholder="Internal positive factors..." id="swotStrengths"></textarea>
                </div>
                <div class="swot-quadrant weaknesses">
                    <h6><i class="fas fa-minus-circle text-warning me-2"></i>Weaknesses</h6>
                    <textarea class="form-control" rows="3" placeholder="Internal negative factors..." id="swotWeaknesses"></textarea>
                </div>
                <div class="swot-quadrant opportunities">
                    <h6><i class="fas fa-arrow-up text-info me-2"></i>Opportunities</h6>
                    <textarea class="form-control" rows="3" placeholder="External positive factors..." id="swotOpportunities"></textarea>
                </div>
                <div class="swot-quadrant threats">
                    <h6><i class="fas fa-exclamation-triangle text-danger me-2"></i>Threats</h6>
                    <textarea class="form-control" rows="3" placeholder="External negative factors..." id="swotThreats"></textarea>
                </div>
            </div>
            <button class="btn btn-outline-primary mt-2" onclick="saveSWOTAnalysis(4)">
                <i class="fas fa-save me-2"></i>Save SWOT Analysis
            </button>
        </div>

        <!-- Progress Tracker -->
        <div class="workspace-section">
            <h5 data-i18n-key="your_progress">Your Progress</h5>
            <div class="progress mb-2">
                <div class="progress-bar" style="width: 20%"></div>
            </div>
            <small class="text-muted">Stage 4 of 20 (20%)</small>
        </div>
    </div>
</div>

<!-- STAGE 5: Needs Discovery - Workspace Column -->
<div class="col-lg-6 workspace-column" id="stage-05-workspace">
    <div class="workspace-header mb-4">
        <h2 data-i18n-key="nav_workspace">Workspace</h2>
        <p class="text-muted">Clinical needs research and validation</p>

        <div class="stage-navigation">
            <a href="stage-04-bilingual.html" class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
            </a>
            <a href="stage-06-bilingual.html" class="btn btn-primary">
                <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>

    <div class="workspace-content">
        <!-- Learning Objectives -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="learning_objectives">Learning Objectives</h4>
            <div class="objectives-list">
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Design effective needs discovery research</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Identify and engage key stakeholders</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Conduct structured interviews and observations</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Prioritize and validate clinical needs</span>
                </div>
            </div>
        </div>

        <!-- Stakeholder Mapping -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="stakeholder_mapping">Stakeholder Mapping</h4>
            <div class="exercise-card">
                <h5>Identify Key Stakeholders</h5>
                <p>Map all stakeholders who influence or are affected by your innovation:</p>

                <div class="stakeholder-categories">
                    <div class="stakeholder-category">
                        <h6><i class="fas fa-user-md me-2"></i>Primary Users</h6>
                        <div class="stakeholder-list" id="primaryUsers">
                            <div class="stakeholder-item">
                                <input type="text" class="form-control mb-2" placeholder="e.g., Cardiologists">
                                <button class="btn btn-sm btn-outline-danger" onclick="removeStakeholder(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addStakeholder('primaryUsers')">
                            <i class="fas fa-plus me-1"></i>Add Primary User
                        </button>
                    </div>

                    <div class="stakeholder-category">
                        <h6><i class="fas fa-users me-2"></i>Secondary Users</h6>
                        <div class="stakeholder-list" id="secondaryUsers">
                            <div class="stakeholder-item">
                                <input type="text" class="form-control mb-2" placeholder="e.g., Nurses, Technicians">
                                <button class="btn btn-sm btn-outline-danger" onclick="removeStakeholder(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addStakeholder('secondaryUsers')">
                            <i class="fas fa-plus me-1"></i>Add Secondary User
                        </button>
                    </div>

                    <div class="stakeholder-category">
                        <h6><i class="fas fa-user-tie me-2"></i>Decision Makers</h6>
                        <div class="stakeholder-list" id="decisionMakers">
                            <div class="stakeholder-item">
                                <input type="text" class="form-control mb-2" placeholder="e.g., Department Heads, Procurement">
                                <button class="btn btn-sm btn-outline-danger" onclick="removeStakeholder(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addStakeholder('decisionMakers')">
                            <i class="fas fa-plus me-1"></i>Add Decision Maker
                        </button>
                    </div>

                    <div class="stakeholder-category">
                        <h6><i class="fas fa-star me-2"></i>Influencers</h6>
                        <div class="stakeholder-list" id="influencers">
                            <div class="stakeholder-item">
                                <input type="text" class="form-control mb-2" placeholder="e.g., Key Opinion Leaders">
                                <button class="btn btn-sm btn-outline-danger" onclick="removeStakeholder(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addStakeholder('influencers')">
                            <i class="fas fa-plus me-1"></i>Add Influencer
                        </button>
                    </div>
                </div>

                <button class="btn btn-primary mt-3" onclick="saveStakeholderMap(5)">
                    <i class="fas fa-save me-2"></i>Save Stakeholder Map
                </button>
            </div>
        </div>

        <!-- Interview Planning -->
        <div class="workspace-section mb-4">
            <h5>Interview Planning</h5>
            <div class="interview-planner">
                <div class="mb-3">
                    <label class="form-label">Interview Type:</label>
                    <select class="form-select" id="interviewType">
                        <option value="">Select type...</option>
                        <option value="structured">Structured Interview</option>
                        <option value="semi-structured">Semi-structured Interview</option>
                        <option value="unstructured">Unstructured Interview</option>
                        <option value="focus-group">Focus Group</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Target Stakeholder:</label>
                    <select class="form-select" id="targetStakeholder">
                        <option value="">Select stakeholder...</option>
                        <option value="physician">Physician</option>
                        <option value="nurse">Nurse</option>
                        <option value="technician">Technician</option>
                        <option value="administrator">Administrator</option>
                        <option value="patient">Patient</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Key Questions:</label>
                    <div id="questionsList">
                        <div class="question-item mb-2">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Enter your question...">
                                <button class="btn btn-outline-danger" onclick="removeQuestion(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="addQuestion()">
                        <i class="fas fa-plus me-1"></i>Add Question
                    </button>
                </div>

                <button class="btn btn-success" onclick="generateInterviewGuide(5)">
                    <i class="fas fa-file-alt me-2"></i>Generate Interview Guide
                </button>
            </div>
        </div>

        <!-- Needs Prioritization Matrix -->
        <div class="workspace-section mb-4">
            <h5>Needs Prioritization</h5>
            <div class="needs-matrix">
                <div class="matrix-header">
                    <h6>Impact vs. Frequency Matrix</h6>
                    <p class="small text-muted">Plot identified needs based on their impact and frequency</p>
                </div>

                <div class="needs-input mb-3">
                    <div class="input-group">
                        <input type="text" class="form-control" id="needDescription" placeholder="Describe the clinical need...">
                        <button class="btn btn-primary" onclick="addNeedToMatrix()">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>

                <div class="priority-matrix">
                    <div class="matrix-quadrant high-impact-high-freq" data-quadrant="high-high">
                        <h6>High Impact, High Frequency</h6>
                        <p class="small">Critical needs - highest priority</p>
                        <div class="needs-list" id="highHigh"></div>
                    </div>
                    <div class="matrix-quadrant high-impact-low-freq" data-quadrant="high-low">
                        <h6>High Impact, Low Frequency</h6>
                        <p class="small">Important but rare - medium priority</p>
                        <div class="needs-list" id="highLow"></div>
                    </div>
                    <div class="matrix-quadrant low-impact-high-freq" data-quadrant="low-high">
                        <h6>Low Impact, High Frequency</h6>
                        <p class="small">Common but minor - medium priority</p>
                        <div class="needs-list" id="lowHigh"></div>
                    </div>
                    <div class="matrix-quadrant low-impact-low-freq" data-quadrant="low-low">
                        <h6>Low Impact, Low Frequency</h6>
                        <p class="small">Minor issues - lowest priority</p>
                        <div class="needs-list" id="lowLow"></div>
                    </div>
                </div>

                <button class="btn btn-primary mt-3" onclick="saveNeedsPrioritization(5)">
                    <i class="fas fa-save me-2"></i>Save Prioritization
                </button>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="workspace-section">
            <h5 data-i18n-key="your_progress">Your Progress</h5>
            <div class="progress mb-2">
                <div class="progress-bar" style="width: 25%"></div>
            </div>
            <small class="text-muted">Stage 5 of 20 (25%)</small>
        </div>
    </div>
</div>

<!-- STAGE 6: Idea Generation - Workspace Column -->
<div class="col-lg-6 workspace-column" id="stage-06-workspace">
    <div class="workspace-header mb-4">
        <h2 data-i18n-key="nav_workspace">Workspace</h2>
        <p class="text-muted">Creative ideation and concept development</p>

        <div class="stage-navigation">
            <a href="stage-05-bilingual.html" class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
            </a>
            <a href="stage-07-bilingual.html" class="btn btn-primary">
                <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>

    <div class="workspace-content">
        <!-- Learning Objectives -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="learning_objectives">Learning Objectives</h4>
            <div class="objectives-list">
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Apply structured brainstorming techniques</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Use analogical thinking for innovation</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Evaluate and refine concepts systematically</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Develop detailed concept descriptions</span>
                </div>
            </div>
        </div>

        <!-- Brainstorming Session -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="brainstorming_session">Brainstorming Session</h4>
            <div class="exercise-card">
                <h5>Structured Idea Generation</h5>
                <p>Generate ideas using proven brainstorming techniques:</p>

                <div class="brainstorming-setup">
                    <div class="mb-3">
                        <label class="form-label">Problem Statement:</label>
                        <textarea class="form-control" rows="2" id="problemStatement"
                                  placeholder="Clearly define the problem you're trying to solve..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Brainstorming Method:</label>
                        <select class="form-select" id="brainstormingMethod">
                            <option value="classic">Classic Brainstorming</option>
                            <option value="brainwriting">Brainwriting 6-3-5</option>
                            <option value="scamper">SCAMPER Technique</option>
                            <option value="analogical">Analogical Thinking</option>
                            <option value="biomimicry">Biomimicry</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Session Duration (minutes):</label>
                        <select class="form-select" id="sessionDuration">
                            <option value="15">15 minutes</option>
                            <option value="30">30 minutes</option>
                            <option value="45">45 minutes</option>
                            <option value="60">60 minutes</option>
                        </select>
                    </div>

                    <button class="btn btn-success" onclick="startBrainstormingSession(6)">
                        <i class="fas fa-play me-2"></i>Start Session
                    </button>
                </div>

                <div id="brainstormingArea" class="brainstorming-area mt-3" style="display: none;">
                    <div class="session-timer">
                        <h6>Time Remaining: <span id="timeRemaining">30:00</span></h6>
                        <div class="progress">
                            <div class="progress-bar" id="timerProgress" style="width: 100%"></div>
                        </div>
                    </div>

                    <div class="idea-input mt-3">
                        <div class="input-group">
                            <input type="text" class="form-control" id="newIdea"
                                   placeholder="Enter your idea..." onkeypress="handleIdeaInput(event)">
                            <button class="btn btn-primary" onclick="addIdea()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                    <div class="ideas-list mt-3">
                        <h6>Generated Ideas (<span id="ideaCount">0</span>):</h6>
                        <div id="ideasContainer" class="ideas-container"></div>
                    </div>

                    <button class="btn btn-warning" onclick="endBrainstormingSession(6)">
                        <i class="fas fa-stop me-2"></i>End Session
                    </button>
                </div>
            </div>
        </div>

        <!-- Concept Development -->
        <div class="workspace-section mb-4">
            <h5>Concept Development</h5>
            <div class="concept-builder">
                <div class="mb-3">
                    <label class="form-label">Select Idea to Develop:</label>
                    <select class="form-select" id="selectedIdea">
                        <option value="">Choose an idea from brainstorming...</option>
                    </select>
                </div>

                <div id="conceptDetails" style="display: none;">
                    <div class="mb-3">
                        <label class="form-label">Concept Name:</label>
                        <input type="text" class="form-control" id="conceptName" placeholder="Give your concept a name...">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Detailed Description:</label>
                        <textarea class="form-control" rows="4" id="conceptDescription"
                                  placeholder="Describe how your concept works..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Key Features:</label>
                        <div id="featuresList">
                            <div class="feature-item mb-2">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Enter a key feature...">
                                    <button class="btn btn-outline-danger" onclick="removeFeature(this)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addFeature()">
                            <i class="fas fa-plus me-1"></i>Add Feature
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Target Users:</label>
                        <input type="text" class="form-control" id="targetUsers"
                               placeholder="Who will use this device?">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Use Case Scenario:</label>
                        <textarea class="form-control" rows="3" id="useCase"
                                  placeholder="Describe a typical use scenario..."></textarea>
                    </div>

                    <button class="btn btn-primary" onclick="saveConcept(6)">
                        <i class="fas fa-save me-2"></i>Save Concept
                    </button>
                </div>
            </div>
        </div>

        <!-- Concept Evaluation -->
        <div class="workspace-section mb-4">
            <h5>Concept Evaluation</h5>
            <div class="evaluation-matrix">
                <div class="mb-3">
                    <label class="form-label">Select Concept to Evaluate:</label>
                    <select class="form-select" id="conceptToEvaluate">
                        <option value="">Choose a developed concept...</option>
                    </select>
                </div>

                <div id="evaluationCriteria" style="display: none;">
                    <h6>Evaluation Criteria (Rate 1-5):</h6>

                    <div class="criteria-group">
                        <div class="criterion">
                            <label>Technical Feasibility:</label>
                            <input type="range" class="form-range" min="1" max="5" value="3" id="evalTechFeasibility">
                            <span class="rating-value">3</span>
                        </div>

                        <div class="criterion">
                            <label>Market Need:</label>
                            <input type="range" class="form-range" min="1" max="5" value="3" id="evalMarketNeed">
                            <span class="rating-value">3</span>
                        </div>

                        <div class="criterion">
                            <label>Novelty/Innovation:</label>
                            <input type="range" class="form-range" min="1" max="5" value="3" id="evalNovelty">
                            <span class="rating-value">3</span>
                        </div>

                        <div class="criterion">
                            <label>Commercial Potential:</label>
                            <input type="range" class="form-range" min="1" max="5" value="3" id="evalCommercial">
                            <span class="rating-value">3</span>
                        </div>

                        <div class="criterion">
                            <label>Development Risk:</label>
                            <input type="range" class="form-range" min="1" max="5" value="3" id="evalRisk">
                            <span class="rating-value">3</span>
                        </div>
                    </div>

                    <div class="evaluation-result mt-3">
                        <h6>Overall Score: <span id="conceptScore">15/25</span></h6>
                        <div class="progress">
                            <div class="progress-bar" id="conceptScoreProgress" style="width: 60%"></div>
                        </div>
                    </div>

                    <button class="btn btn-success" onclick="saveConceptEvaluation(6)">
                        <i class="fas fa-save me-2"></i>Save Evaluation
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="workspace-section">
            <h5 data-i18n-key="your_progress">Your Progress</h5>
            <div class="progress mb-2">
                <div class="progress-bar" style="width: 30%"></div>
            </div>
            <small class="text-muted">Stage 6 of 20 (30%)</small>
        </div>
    </div>
</div>

<!-- STAGE 7: Testing Your Idea - Workspace Column -->
<div class="col-lg-6 workspace-column" id="stage-07-workspace">
    <div class="workspace-header mb-4">
        <h2 data-i18n-key="nav_workspace">Workspace</h2>
        <p class="text-muted">Prototype development and validation testing</p>

        <div class="stage-navigation">
            <a href="stage-06-bilingual.html" class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
            </a>
            <a href="stage-08-bilingual.html" class="btn btn-primary">
                <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>

    <div class="workspace-content">
        <!-- Learning Objectives -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="learning_objectives">Learning Objectives</h4>
            <div class="objectives-list">
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Design appropriate validation experiments</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Create and test prototypes effectively</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Gather and analyze user feedback</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Iterate based on testing results</span>
                </div>
            </div>
        </div>

        <!-- Prototype Planning -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="prototype_planning">Prototype Planning</h4>
            <div class="exercise-card">
                <h5>Prototype Development Strategy</h5>
                <p>Plan your prototype development approach:</p>

                <div class="prototype-strategy">
                    <div class="mb-3">
                        <label class="form-label">Prototype Type:</label>
                        <select class="form-select" id="prototypeType">
                            <option value="">Select type...</option>
                            <option value="paper">Paper/Digital Mockup</option>
                            <option value="functional">Functional Prototype</option>
                            <option value="appearance">Appearance Model</option>
                            <option value="proof-of-concept">Proof of Concept</option>
                            <option value="alpha">Alpha Prototype</option>
                            <option value="beta">Beta Prototype</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Primary Testing Goal:</label>
                        <select class="form-select" id="testingGoal">
                            <option value="">Select goal...</option>
                            <option value="usability">Usability Testing</option>
                            <option value="functionality">Functionality Validation</option>
                            <option value="safety">Safety Assessment</option>
                            <option value="performance">Performance Testing</option>
                            <option value="user-acceptance">User Acceptance</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Target Users for Testing:</label>
                        <input type="text" class="form-control" id="testUsers"
                               placeholder="Who will test your prototype?">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Testing Environment:</label>
                        <select class="form-select" id="testEnvironment">
                            <option value="">Select environment...</option>
                            <option value="lab">Laboratory Setting</option>
                            <option value="simulated">Simulated Clinical Environment</option>
                            <option value="clinical">Actual Clinical Setting</option>
                            <option value="home">Home/Patient Environment</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Success Criteria:</label>
                        <textarea class="form-control" rows="3" id="successCriteria"
                                  placeholder="Define what success looks like for this test..."></textarea>
                    </div>

                    <button class="btn btn-primary" onclick="savePrototypePlan(7)">
                        <i class="fas fa-save me-2"></i>Save Prototype Plan
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Design -->
        <div class="workspace-section mb-4">
            <h5>Test Design</h5>
            <div class="test-designer">
                <div class="mb-3">
                    <label class="form-label">Test Method:</label>
                    <select class="form-select" id="testMethod">
                        <option value="">Select method...</option>
                        <option value="user-observation">User Observation</option>
                        <option value="task-analysis">Task Analysis</option>
                        <option value="ab-testing">A/B Testing</option>
                        <option value="heuristic">Heuristic Evaluation</option>
                        <option value="cognitive-walkthrough">Cognitive Walkthrough</option>
                        <option value="stress-testing">Stress Testing</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Number of Participants:</label>
                    <input type="number" class="form-control" id="participantCount" min="1" max="100" value="5">
                </div>

                <div class="mb-3">
                    <label class="form-label">Test Duration (minutes):</label>
                    <input type="number" class="form-control" id="testDuration" min="5" max="180" value="30">
                </div>

                <div class="mb-3">
                    <label class="form-label">Test Tasks:</label>
                    <div id="testTasks">
                        <div class="task-item mb-2">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Describe a test task...">
                                <button class="btn btn-outline-danger" onclick="removeTestTask(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="addTestTask()">
                        <i class="fas fa-plus me-1"></i>Add Task
                    </button>
                </div>

                <div class="mb-3">
                    <label class="form-label">Metrics to Measure:</label>
                    <div class="metrics-checklist">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="metricTime">
                            <label class="form-check-label" for="metricTime">Task Completion Time</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="metricSuccess">
                            <label class="form-check-label" for="metricSuccess">Success Rate</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="metricErrors">
                            <label class="form-check-label" for="metricErrors">Error Rate</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="metricSatisfaction">
                            <label class="form-check-label" for="metricSatisfaction">User Satisfaction</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="metricLearnability">
                            <label class="form-check-label" for="metricLearnability">Learnability</label>
                        </div>
                    </div>
                </div>

                <button class="btn btn-success" onclick="generateTestProtocol(7)">
                    <i class="fas fa-file-alt me-2"></i>Generate Test Protocol
                </button>
            </div>
        </div>

        <!-- Test Results Tracker -->
        <div class="workspace-section mb-4">
            <h5>Test Results</h5>
            <div class="results-tracker">
                <div class="mb-3">
                    <label class="form-label">Test Session:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="sessionName" placeholder="Test session name...">
                        <button class="btn btn-primary" onclick="startTestSession(7)">
                            <i class="fas fa-play me-1"></i>Start
                        </button>
                    </div>
                </div>

                <div id="activeSession" style="display: none;">
                    <div class="session-info">
                        <h6>Active Session: <span id="currentSession"></span></h6>
                        <p>Participant: <span id="currentParticipant">1</span> of <span id="totalParticipants">5</span></p>
                    </div>

                    <div class="participant-data">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Task Completion Time (seconds):</label>
                                <input type="number" class="form-control" id="completionTime">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Success Rate (%):</label>
                                <input type="number" class="form-control" id="successRate" min="0" max="100">
                            </div>
                        </div>

                        <div class="mt-3">
                            <label class="form-label">Observations:</label>
                            <textarea class="form-control" rows="3" id="observations"
                                      placeholder="Record observations about this participant..."></textarea>
                        </div>

                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="recordParticipantData(7)">
                                <i class="fas fa-save me-2"></i>Record Data
                            </button>
                            <button class="btn btn-outline-secondary" onclick="nextParticipant(7)">
                                <i class="fas fa-arrow-right me-2"></i>Next Participant
                            </button>
                            <button class="btn btn-success" onclick="completeTestSession(7)">
                                <i class="fas fa-check me-2"></i>Complete Session
                            </button>
                        </div>
                    </div>
                </div>

                <div id="resultsAnalysis" class="mt-3">
                    <h6>Test Results Summary:</h6>
                    <div id="resultsSummary" class="results-summary">
                        <p class="text-muted">No test data recorded yet.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="workspace-section">
            <h5 data-i18n-key="your_progress">Your Progress</h5>
            <div class="progress mb-2">
                <div class="progress-bar" style="width: 35%"></div>
            </div>
            <small class="text-muted">Stage 7 of 20 (35%)</small>
        </div>
    </div>
</div>

<!-- STAGE 8: Clinical Trials - Workspace Column -->
<div class="col-lg-6 workspace-column" id="stage-08-workspace">
    <div class="workspace-header mb-4">
        <h2 data-i18n-key="nav_workspace">Workspace</h2>
        <p class="text-muted">Clinical study design and execution</p>

        <div class="stage-navigation">
            <a href="stage-07-bilingual.html" class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
            </a>
            <a href="stage-09-bilingual.html" class="btn btn-primary">
                <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>

    <div class="workspace-content">
        <!-- Learning Objectives -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="learning_objectives">Learning Objectives</h4>
            <div class="objectives-list">
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Design appropriate clinical study protocols</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Understand regulatory requirements for trials</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Plan patient recruitment and retention strategies</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Develop data collection and analysis plans</span>
                </div>
            </div>
        </div>

        <!-- Clinical Study Designer -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="clinical_study_designer">Clinical Study Designer</h4>
            <div class="exercise-card">
                <h5>Study Protocol Development</h5>
                <p>Design your clinical study protocol:</p>

                <div class="study-design">
                    <div class="mb-3">
                        <label class="form-label">Study Phase:</label>
                        <select class="form-select" id="studyPhase">
                            <option value="">Select phase...</option>
                            <option value="feasibility">Feasibility Study</option>
                            <option value="pilot">Pilot Study</option>
                            <option value="phase1">Phase I (Safety)</option>
                            <option value="phase2">Phase II (Efficacy)</option>
                            <option value="phase3">Phase III (Confirmatory)</option>
                            <option value="phase4">Phase IV (Post-Market)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Study Design:</label>
                        <select class="form-select" id="studyDesign">
                            <option value="">Select design...</option>
                            <option value="rct">Randomized Controlled Trial</option>
                            <option value="cohort">Cohort Study</option>
                            <option value="case-control">Case-Control Study</option>
                            <option value="crossover">Crossover Study</option>
                            <option value="single-arm">Single-Arm Study</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Primary Endpoint:</label>
                        <input type="text" class="form-control" id="primaryEndpoint"
                               placeholder="Define the primary outcome measure...">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Secondary Endpoints:</label>
                        <div id="secondaryEndpoints">
                            <div class="endpoint-item mb-2">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Secondary endpoint...">
                                    <button class="btn btn-outline-danger" onclick="removeEndpoint(this)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addSecondaryEndpoint()">
                            <i class="fas fa-plus me-1"></i>Add Secondary Endpoint
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Target Sample Size:</label>
                            <input type="number" class="form-control" id="sampleSize" min="1" placeholder="Number of participants">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Study Duration (months):</label>
                            <input type="number" class="form-control" id="studyDuration" min="1" placeholder="Duration">
                        </div>
                    </div>

                    <div class="mb-3 mt-3">
                        <label class="form-label">Inclusion Criteria:</label>
                        <textarea class="form-control" rows="3" id="inclusionCriteria"
                                  placeholder="Define who can participate in the study..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Exclusion Criteria:</label>
                        <textarea class="form-control" rows="3" id="exclusionCriteria"
                                  placeholder="Define who cannot participate in the study..."></textarea>
                    </div>

                    <button class="btn btn-primary" onclick="generateStudyProtocol(8)">
                        <i class="fas fa-file-medical me-2"></i>Generate Protocol
                    </button>
                </div>
            </div>
        </div>

        <!-- Regulatory Pathway -->
        <div class="workspace-section mb-4">
            <h5>Regulatory Pathway</h5>
            <div class="regulatory-planner">
                <div class="mb-3">
                    <label class="form-label">Target Market:</label>
                    <select class="form-select" id="regulatoryMarket">
                        <option value="">Select market...</option>
                        <option value="fda">FDA (United States)</option>
                        <option value="ce">CE Mark (Europe)</option>
                        <option value="health-canada">Health Canada</option>
                        <option value="tga">TGA (Australia)</option>
                        <option value="pmda">PMDA (Japan)</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Device Classification:</label>
                    <select class="form-select" id="deviceClass">
                        <option value="">Select class...</option>
                        <option value="class1">Class I (Low Risk)</option>
                        <option value="class2">Class II (Moderate Risk)</option>
                        <option value="class3">Class III (High Risk)</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Regulatory Strategy:</label>
                    <select class="form-select" id="regulatoryStrategy">
                        <option value="">Select strategy...</option>
                        <option value="510k">510(k) Premarket Notification</option>
                        <option value="pma">Premarket Approval (PMA)</option>
                        <option value="de-novo">De Novo Classification</option>
                        <option value="ide">Investigational Device Exemption (IDE)</option>
                    </select>
                </div>

                <div id="regulatoryTimeline" class="regulatory-timeline">
                    <h6>Regulatory Timeline:</h6>
                    <div id="timelineSteps" class="timeline-steps">
                        <p class="text-muted">Select regulatory strategy to see timeline...</p>
                    </div>
                </div>

                <button class="btn btn-info" onclick="generateRegulatoryPlan(8)">
                    <i class="fas fa-gavel me-2"></i>Generate Regulatory Plan
                </button>
            </div>
        </div>

        <!-- Patient Recruitment -->
        <div class="workspace-section mb-4">
            <h5>Patient Recruitment Strategy</h5>
            <div class="recruitment-planner">
                <div class="mb-3">
                    <label class="form-label">Target Patient Population:</label>
                    <input type="text" class="form-control" id="targetPopulation"
                           placeholder="Describe your target patient population...">
                </div>

                <div class="mb-3">
                    <label class="form-label">Recruitment Channels:</label>
                    <div class="recruitment-channels">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="channelClinics">
                            <label class="form-check-label" for="channelClinics">Partner Clinics/Hospitals</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="channelReferrals">
                            <label class="form-check-label" for="channelReferrals">Physician Referrals</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="channelAdvertising">
                            <label class="form-check-label" for="channelAdvertising">Patient Advertising</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="channelRegistries">
                            <label class="form-check-label" for="channelRegistries">Patient Registries</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="channelSocial">
                            <label class="form-check-label" for="channelSocial">Social Media</label>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Recruitment Rate (patients/month):</label>
                        <input type="number" class="form-control" id="recruitmentRate" min="1">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Expected Dropout Rate (%):</label>
                        <input type="number" class="form-control" id="dropoutRate" min="0" max="100">
                    </div>
                </div>

                <div class="mb-3 mt-3">
                    <label class="form-label">Retention Strategies:</label>
                    <textarea class="form-control" rows="3" id="retentionStrategies"
                              placeholder="How will you keep patients engaged in the study?"></textarea>
                </div>

                <button class="btn btn-success" onclick="saveRecruitmentPlan(8)">
                    <i class="fas fa-users me-2"></i>Save Recruitment Plan
                </button>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="workspace-section">
            <h5 data-i18n-key="your_progress">Your Progress</h5>
            <div class="progress mb-2">
                <div class="progress-bar" style="width: 40%"></div>
            </div>
            <small class="text-muted">Stage 8 of 20 (40%)</small>
        </div>
    </div>
</div>

<!-- STAGE 9: Reliability Considerations - Workspace Column -->
<div class="col-lg-6 workspace-column" id="stage-09-workspace">
    <div class="workspace-header mb-4">
        <h2 data-i18n-key="nav_workspace">Workspace</h2>
        <p class="text-muted">Risk management and reliability engineering</p>

        <div class="stage-navigation">
            <a href="stage-08-bilingual.html" class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> <span data-i18n-key="previous">Previous</span>
            </a>
            <a href="stage-10-bilingual.html" class="btn btn-primary">
                <span data-i18n-key="next">Next</span> <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>

    <div class="workspace-content">
        <!-- Learning Objectives -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="learning_objectives">Learning Objectives</h4>
            <div class="objectives-list">
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Conduct systematic risk analysis (FMEA)</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Implement risk control measures</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Design reliability testing protocols</span>
                </div>
                <div class="objective-item">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Establish quality management systems</span>
                </div>
            </div>
        </div>

        <!-- FMEA Analysis -->
        <div class="workspace-section mb-4">
            <h4 data-i18n-key="fmea_analysis">FMEA Analysis</h4>
            <div class="exercise-card">
                <h5>Failure Mode and Effects Analysis</h5>
                <p>Systematically identify and analyze potential failure modes:</p>

                <div class="fmea-builder">
                    <div class="mb-3">
                        <label class="form-label">Device Component/Function:</label>
                        <input type="text" class="form-control" id="deviceComponent"
                               placeholder="e.g., Battery, Sensor, User Interface">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Potential Failure Mode:</label>
                        <input type="text" class="form-control" id="failureMode"
                               placeholder="How might this component fail?">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Potential Effects:</label>
                        <textarea class="form-control" rows="2" id="failureEffects"
                                  placeholder="What are the consequences of this failure?"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Potential Causes:</label>
                        <textarea class="form-control" rows="2" id="failureCauses"
                                  placeholder="What could cause this failure?"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Severity (1-10):</label>
                            <select class="form-select" id="severity">
                                <option value="">Select...</option>
                                <option value="1">1 - No effect</option>
                                <option value="2">2 - Very minor</option>
                                <option value="3">3 - Minor</option>
                                <option value="4">4 - Very low</option>
                                <option value="5">5 - Low</option>
                                <option value="6">6 - Moderate</option>
                                <option value="7">7 - High</option>
                                <option value="8">8 - Very high</option>
                                <option value="9">9 - Hazardous</option>
                                <option value="10">10 - Catastrophic</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Occurrence (1-10):</label>
                            <select class="form-select" id="occurrence">
                                <option value="">Select...</option>
                                <option value="1">1 - Remote</option>
                                <option value="2">2 - Very low</option>
                                <option value="3">3 - Low</option>
                                <option value="4">4 - Moderately low</option>
                                <option value="5">5 - Moderate</option>
                                <option value="6">6 - Moderately high</option>
                                <option value="7">7 - High</option>
                                <option value="8">8 - Very high</option>
                                <option value="9">9 - Extremely high</option>
                                <option value="10">10 - Almost certain</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Detection (1-10):</label>
                            <select class="form-select" id="detection">
                                <option value="">Select...</option>
                                <option value="1">1 - Almost certain</option>
                                <option value="2">2 - Very high</option>
                                <option value="3">3 - High</option>
                                <option value="4">4 - Moderately high</option>
                                <option value="5">5 - Moderate</option>
                                <option value="6">6 - Low</option>
                                <option value="7">7 - Very low</option>
                                <option value="8">8 - Remote</option>
                                <option value="9">9 - Very remote</option>
                                <option value="10">10 - Absolute uncertainty</option>
                            </select>
                        </div>
                    </div>

                    <div class="rpn-calculation mt-3">
                        <h6>Risk Priority Number (RPN): <span id="rpnValue">-</span></h6>
                        <div class="progress">
                            <div class="progress-bar" id="rpnProgress" style="width: 0%"></div>
                        </div>
                        <small id="rpnInterpretation" class="text-muted">Enter severity, occurrence, and detection values</small>
                    </div>

                    <div class="mb-3 mt-3">
                        <label class="form-label">Recommended Actions:</label>
                        <textarea class="form-control" rows="2" id="recommendedActions"
                                  placeholder="What actions should be taken to reduce risk?"></textarea>
                    </div>

                    <button class="btn btn-primary" onclick="addFMEAEntry(9)">
                        <i class="fas fa-plus me-2"></i>Add FMEA Entry
                    </button>
                </div>

                <div class="fmea-table mt-4">
                    <h6>FMEA Summary:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm" id="fmeaTable">
                            <thead>
                                <tr>
                                    <th>Component</th>
                                    <th>Failure Mode</th>
                                    <th>Effects</th>
                                    <th>S</th>
                                    <th>O</th>
                                    <th>D</th>
                                    <th>RPN</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="fmeaTableBody">
                                <tr>
                                    <td colspan="8" class="text-center text-muted">No FMEA entries yet</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Control Measures -->
        <div class="workspace-section mb-4">
            <h5>Risk Control Measures</h5>
            <div class="risk-controls">
                <div class="control-hierarchy">
                    <h6>Risk Control Hierarchy (ISO 14971):</h6>

                    <div class="control-level" data-level="1">
                        <h6><i class="fas fa-shield-alt text-success me-2"></i>1. Inherent Safety by Design</h6>
                        <div class="control-measures" id="inherentSafety">
                            <div class="measure-item">
                                <input type="text" class="form-control mb-2" placeholder="Design-based safety measure...">
                                <button class="btn btn-sm btn-outline-danger" onclick="removeMeasure(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addRiskMeasure('inherentSafety')">
                            <i class="fas fa-plus me-1"></i>Add Measure
                        </button>
                    </div>

                    <div class="control-level" data-level="2">
                        <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>2. Protective Measures</h6>
                        <div class="control-measures" id="protectiveMeasures">
                            <div class="measure-item">
                                <input type="text" class="form-control mb-2" placeholder="Protective safety measure...">
                                <button class="btn btn-sm btn-outline-danger" onclick="removeMeasure(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addRiskMeasure('protectiveMeasures')">
                            <i class="fas fa-plus me-1"></i>Add Measure
                        </button>
                    </div>

                    <div class="control-level" data-level="3">
                        <h6><i class="fas fa-info-circle text-info me-2"></i>3. Information for Safety</h6>
                        <div class="control-measures" id="informationSafety">
                            <div class="measure-item">
                                <input type="text" class="form-control mb-2" placeholder="Information-based safety measure...">
                                <button class="btn btn-sm btn-outline-danger" onclick="removeMeasure(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="addRiskMeasure('informationSafety')">
                            <i class="fas fa-plus me-1"></i>Add Measure
                        </button>
                    </div>
                </div>

                <button class="btn btn-success mt-3" onclick="saveRiskControls(9)">
                    <i class="fas fa-save me-2"></i>Save Risk Controls
                </button>
            </div>
        </div>

        <!-- Reliability Testing -->
        <div class="workspace-section mb-4">
            <h5>Reliability Testing Plan</h5>
            <div class="reliability-planner">
                <div class="mb-3">
                    <label class="form-label">Test Type:</label>
                    <select class="form-select" id="reliabilityTestType">
                        <option value="">Select test type...</option>
                        <option value="accelerated-life">Accelerated Life Testing</option>
                        <option value="environmental">Environmental Testing</option>
                        <option value="mechanical">Mechanical Testing</option>
                        <option value="electrical">Electrical Safety Testing</option>
                        <option value="biocompatibility">Biocompatibility Testing</option>
                        <option value="sterilization">Sterilization Validation</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Test Standard:</label>
                    <input type="text" class="form-control" id="testStandard"
                           placeholder="e.g., IEC 60601-1, ISO 10993">
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Sample Size:</label>
                        <input type="number" class="form-control" id="testSampleSize" min="1">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Test Duration:</label>
                        <input type="text" class="form-control" id="testDuration" placeholder="e.g., 1000 hours">
                    </div>
                </div>

                <div class="mb-3 mt-3">
                    <label class="form-label">Test Conditions:</label>
                    <textarea class="form-control" rows="3" id="testConditions"
                              placeholder="Describe environmental conditions, stress levels, etc."></textarea>
                </div>

                <div class="mb-3">
                    <label class="form-label">Acceptance Criteria:</label>
                    <textarea class="form-control" rows="2" id="acceptanceCriteria"
                              placeholder="Define what constitutes passing the test..."></textarea>
                </div>

                <button class="btn btn-info" onclick="addReliabilityTest(9)">
                    <i class="fas fa-flask me-2"></i>Add Test to Plan
                </button>
            </div>

            <div class="testing-schedule mt-4">
                <h6>Testing Schedule:</h6>
                <div id="testingSchedule" class="schedule-list">
                    <p class="text-muted">No tests scheduled yet.</p>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="workspace-section">
            <h5 data-i18n-key="your_progress">Your Progress</h5>
            <div class="progress mb-2">
                <div class="progress-bar" style="width: 45%"></div>
            </div>
            <small class="text-muted">Stage 9 of 20 (45%)</small>
        </div>
    </div>
</div>
