import React, { createContext, useContext, useState, ReactNode } from 'react';

type Language = 'ar' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const translations = {
  ar: {
    // Navigation
    'table_of_contents': 'جدول المحتويات',
    'interactive_lectures': 'المحاضرات التفاعلية',
    'book_info': 'معلومات الكتاب',
    'author': 'المؤلف',
    'year': 'السنة',
    'publisher': 'الناشر',
    'current_chapter': 'الفصل الحالي',
    'search_placeholder': 'البحث في الكتاب...',
    'chapter': 'الفصل',
    'lecture': 'المحاضرة',
    
    // Content
    'exercises_discussion': 'تمارين وأسئلة نقاش',
    'references_sources': 'المراجع والمصادر',
    'reading_time': 'وقت القراءة: 25-30 دقيقة',
    'level_intermediate': 'مستوى: متوسط',
    'discussion': 'نقاش',
    'practical': 'تطبيقي',
    'research': 'بحثي',
    'journal': 'مجلة علمية',
    'book': 'كتاب',
    'website': 'موقع',
    'patent': 'براءة اختراع',
    
    // Actions
    'previous_chapter': 'الفصل السابق',
    'next_chapter': 'الفصل التالي',
    'back_to_top': 'العودة للأعلى',
    'bookmark': 'حفظ كإشارة مرجعية',
    'share': 'مشاركة',
    'download_chapter': 'تحميل الفصل',
    
    // Lectures
    'duration': 'المدة',
    'minutes': 'دقيقة',
    'watch_lecture': 'مشاهدة المحاضرة',
    'download_materials': 'تحميل المواد',
    'take_quiz': 'إجراء الاختبار',
    'lecture_materials': 'مواد المحاضرة',
    'quiz_results': 'نتائج الاختبار',
    
    // Footer
    'academic_book': 'كتاب أكاديمي متخصص في الهندسة الطبية الحيوية',
    'copyright': 'جميع الحقوق محفوظة',
    'contact': 'للتواصل',
    
    // Error messages
    'chapter_not_found': 'فصل غير موجود',
    'chapter_not_available': 'الفصل المطلوب غير متوفر في الكتاب'
  },
  en: {
    // Navigation
    'table_of_contents': 'Table of Contents',
    'interactive_lectures': 'Interactive Lectures',
    'book_info': 'Book Information',
    'author': 'Author',
    'year': 'Year',
    'publisher': 'Publisher',
    'current_chapter': 'Current Chapter',
    'search_placeholder': 'Search in book...',
    'chapter': 'Chapter',
    'lecture': 'Lecture',
    
    // Content
    'exercises_discussion': 'Exercises and Discussion Questions',
    'references_sources': 'References and Sources',
    'reading_time': 'Reading time: 25-30 minutes',
    'level_intermediate': 'Level: Intermediate',
    'discussion': 'Discussion',
    'practical': 'Practical',
    'research': 'Research',
    'journal': 'Journal',
    'book': 'Book',
    'website': 'Website',
    'patent': 'Patent',
    
    // Actions
    'previous_chapter': 'Previous Chapter',
    'next_chapter': 'Next Chapter',
    'back_to_top': 'Back to Top',
    'bookmark': 'Bookmark',
    'share': 'Share',
    'download_chapter': 'Download Chapter',
    
    // Lectures
    'duration': 'Duration',
    'minutes': 'minutes',
    'watch_lecture': 'Watch Lecture',
    'download_materials': 'Download Materials',
    'take_quiz': 'Take Quiz',
    'lecture_materials': 'Lecture Materials',
    'quiz_results': 'Quiz Results',
    
    // Footer
    'academic_book': 'Academic book specialized in Biomedical Engineering',
    'copyright': 'All rights reserved',
    'contact': 'Contact',
    
    // Error messages
    'chapter_not_found': 'Chapter Not Found',
    'chapter_not_available': 'The requested chapter is not available in the book'
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('ar');

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['ar']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};