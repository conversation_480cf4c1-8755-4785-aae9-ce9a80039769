import React from 'react';
import { BookOpen, Menu, Search } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import LanguageToggle from './LanguageToggle';

interface HeaderProps {
  onMenuClick: () => void;
  currentChapter: string;
}

const Header: React.FC<HeaderProps> = ({ onMenuClick, currentChapter }) => {
  const { language, t } = useLanguage();

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={onMenuClick}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors lg:hidden"
            >
              <Menu className="h-6 w-6 text-gray-600" />
            </button>
            
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="p-2 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-gray-900">
                  {language === 'ar' ? 'دليل البحث والابتكار' : 'Research & Innovation Guide'}
                </h1>
                <p className="text-sm text-gray-500">
                  {language === 'ar' ? 'في الأجهزة الطبية' : 'in Medical Devices'}
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="hidden md:flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <span>{t('current_chapter')}: {currentChapter}</span>
            </div>
            
            <div className="relative hidden sm:block">
              <Search className={`h-5 w-5 text-gray-400 absolute ${language === 'ar' ? 'right-3' : 'left-3'} top-3`} />
              <input
                type="text"
                placeholder={t('search_placeholder')}
                className={`${language === 'ar' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm`}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <LanguageToggle />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;