/* Medical Device Innovation Guide - Custom Styles */

/* Root Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gradient-primary: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    --gradient-secondary: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.5rem;
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #ffffff;
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

/* Feature Cards */
.feature-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

/* Stage Preview Cards */
.stage-preview-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.stage-preview-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.stage-number {
    display: inline-block;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

/* Main Content Layout */
.main-content {
    padding-top: 80px;
    min-height: calc(100vh - 80px);
}

/* Dashboard Styles */
.sidebar {
    background: var(--light-color);
    min-height: calc(100vh - 80px);
    padding: 2rem 1rem;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-content {
    position: sticky;
    top: 100px;
}

.sidebar-title {
    color: var(--dark-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.progress-overview {
    text-align: center;
}

.progress-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--light-color) 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 90px;
    height: 90px;
    background: white;
    border-radius: 50%;
    position: absolute;
}

.progress-text {
    position: relative;
    z-index: 1;
    text-align: center;
}

.progress-percentage {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Stage Groups */
.stage-group {
    margin-bottom: 2rem;
}

.stage-group-title {
    color: var(--secondary-color);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
}

.stage-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stage-link {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stage-link:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.stage-link .stage-number {
    width: 30px;
    height: 30px;
    background: var(--light-color);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.stage-link:hover .stage-number {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.stage-title {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Dashboard Main Content */
.dashboard-main {
    padding: 2rem;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: var(--secondary-color);
    font-size: 1.125rem;
}

/* Stat Cards */
.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--dark-color);
}

.stat-label {
    color: var(--secondary-color);
    margin: 0;
    font-size: 0.875rem;
}

/* Stages Grid */
.stages-grid {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

.stage-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
}

.stage-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.stage-card-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem;
    text-align: center;
}

.stage-card-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stage-card-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.stage-card-body {
    padding: 1.5rem;
}

.stage-card-description {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.stage-card-progress {
    margin-bottom: 1rem;
}

.stage-card-actions {
    display: flex;
    gap: 0.5rem;
}

/* Activity List */
.activity-list {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    gap: 1rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin: 0;
    font-weight: 500;
}

.activity-time {
    color: var(--secondary-color);
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: var(--secondary-color);
}

/* Resource Categories */
.resource-category {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
}

.category-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.category-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.category-header h3 {
    margin: 0;
    font-weight: 600;
}

.resource-list {
    padding: 1.5rem;
}

.resource-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    margin-bottom: 0.5rem;
    border: 1px solid transparent;
}

.resource-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
    text-decoration: none;
    border-color: var(--primary-color);
}

.resource-item i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.resource-item span {
    flex: 1;
    font-weight: 500;
}

.resource-item small {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    background: var(--light-color);
    border-radius: 1rem;
}

/* Interactive Tools */
.interactive-tool {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    text-align: center;
    height: 100%;
    transition: var(--transition);
}

.interactive-tool:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.tool-header {
    margin-bottom: 1rem;
}

.tool-header i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.tool-header h4 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.tool-description {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

/* Quick Links */
.quick-links {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-link-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    height: 100%;
}

.quick-link-card:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.quick-link-card i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
}

.quick-link-card span {
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .dashboard-main {
        padding: 1rem;
    }
    
    .sidebar {
        padding: 1rem;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .dashboard-title {
        font-size: 2rem;
    }
    
    .stage-navigation {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .stage-navigation .btn {
        width: 100%;
    }
}

/* Stage Content Styles */
.stage-content {
    padding-top: 80px;
    min-height: calc(100vh - 80px);
}

.stage-header {
    background: var(--light-color);
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.stage-breadcrumb {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.stage-breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.stage-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stage-subtitle {
    font-size: 1.125rem;
    color: var(--secondary-color);
}

.progress-circle-small {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.stage-navigation {
    display: flex;
    gap: 0.5rem;
}

/* Stage Body */
.stage-body {
    padding: 2rem 0;
}

.learning-objectives {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
}

.learning-objectives h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.learning-objectives ul {
    margin: 0;
    padding-left: 1.5rem;
}

.learning-objectives li {
    margin-bottom: 0.5rem;
}

.stage-content-section {
    margin-bottom: 3rem;
}

.stage-content-section h3 {
    color: var(--dark-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.stage-content-section h4 {
    color: var(--dark-color);
    font-weight: 600;
    margin: 2rem 0 1rem;
}

/* Interactive Elements */
.interactive-section {
    background: white;
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin: 2rem 0;
}

.exercise-card {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.reflection-questions .question-item {
    margin-bottom: 1.5rem;
}

.reflection-questions label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: block;
}

/* Key Takeaways */
.key-takeaways {
    background: var(--success-color);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin: 2rem 0;
}

.key-takeaways h3 {
    color: white;
    margin-bottom: 1rem;
}

.key-takeaways ul {
    margin: 0;
    padding-left: 1.5rem;
}

.key-takeaways li {
    margin-bottom: 0.75rem;
}

/* Stage Sidebar */
.stage-sidebar {
    padding: 2rem 1rem;
}

.stage-sidebar > div {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.stage-sidebar h5 {
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.progress-tracker .progress {
    height: 8px;
    background: var(--light-color);
}

.nav-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.nav-link-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
}

.nav-link-item:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.nav-link-item .nav-number {
    width: 25px;
    height: 25px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.75rem;
    margin-right: 0.75rem;
}

.nav-link-item:hover .nav-number {
    background: white;
    color: var(--primary-color);
}

.nav-link-item .nav-title {
    font-size: 0.875rem;
    font-weight: 500;
}

.resource-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.resource-link {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
}

.resource-link:hover {
    background: var(--light-color);
    color: var(--primary-color);
    text-decoration: none;
}

.resource-link i {
    margin-right: 0.5rem;
    width: 16px;
    text-align: center;
}

/* Specialized Stage Content */
.market-stats .stat-box {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-box h4 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-box p {
    color: var(--secondary-color);
    margin: 0;
    font-weight: 500;
}

.classification-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.classification-item {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.class-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.class-header h5 {
    margin: 0;
    font-weight: 700;
}

.risk-level {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.risk-level.low {
    background: var(--success-color);
    color: white;
}

.risk-level.medium {
    background: var(--warning-color);
    color: var(--dark-color);
}

.risk-level.high {
    background: var(--danger-color);
    color: white;
}

.examples {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--light-color);
    font-size: 0.875rem;
}

.market-segments .segment-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.segment-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.segment-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
}

.segment-card h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.segment-card p {
    color: var(--secondary-color);
    margin: 0;
    font-weight: 500;
}

.trends-section .trend-item {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.trend-item h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.trend-item p {
    color: var(--secondary-color);
    margin: 0;
}

/* Process Diagram */
.process-diagram {
    margin: 2rem 0;
}

.phase-timeline {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.phase-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.phase-number {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.phase-content h5 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.phase-content p {
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

.phase-content small {
    color: var(--info-color);
    font-weight: 600;
}

/* Methodology Cards */
.methodology-cards {
    margin: 2rem 0;
}

.methodology-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.methodology-card h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.methodology-card p {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.methodology-steps {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.methodology-steps span {
    background: var(--light-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Success Factors */
.success-factors {
    margin: 2rem 0;
}

.factor-item {
    display: flex;
    align-items: flex-start;
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    gap: 1rem;
}

.factor-item i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.factor-item h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.factor-item p {
    color: var(--secondary-color);
    margin: 0;
}

/* Ecosystem Diagram */
.ecosystem-diagram {
    margin: 2rem 0;
}

.ecosystem-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
    transition: var(--transition);
}

.ecosystem-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.ecosystem-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.ecosystem-card h5 {
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.ecosystem-card p {
    color: var(--secondary-color);
    margin: 0;
    font-size: 0.875rem;
}

/* Print Styles */
@media print {
    .navbar,
    .stage-navigation,
    .sidebar {
        display: none !important;
    }

    .main-content {
        padding-top: 0 !important;
    }

    .stage-content {
        padding-top: 0 !important;
    }
}
