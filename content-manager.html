<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Management - Medical Device Innovation Guide</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">
    
    <style>
        .content-editor {
            min-height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
        }
        .stage-selector {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .language-tabs .nav-link {
            border-radius: 8px 8px 0 0;
        }
        .content-preview {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }
        .section-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 1rem;
            padding: 1rem;
        }
        .section-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-cogs me-3"></i>Content Management System</h1>
                    <p class="mb-0">Manage bilingual content for the Medical Device Innovation Guide</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="index.html" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i>Back to Guide
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Stage Selector -->
        <div class="stage-selector">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <label class="form-label fw-bold">Select Stage:</label>
                    <select class="form-select" id="stageSelector">
                        <option value="">Choose a stage...</option>
                        <option value="stage_01_introduction">Stage 1: General Introduction</option>
                        <option value="stage_02_landscape">Stage 2: Medical Device Landscape</option>
                        <option value="stage_03_process">Stage 3: Innovation Process</option>
                        <option value="stage_04_opportunity">Stage 4: Understanding Opportunity</option>
                        <option value="stage_05_needs">Stage 5: Needs Discovery</option>
                        <option value="stage_06_ideation">Stage 6: Idea Generation</option>
                        <option value="stage_07_testing">Stage 7: Testing Your Idea</option>
                        <option value="stage_08_clinical">Stage 8: Clinical Trials</option>
                        <option value="stage_09_reliability">Stage 9: Reliability Considerations</option>
                        <option value="stage_10_documentation">Stage 10: Innovation Notebooks</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-bold">Actions:</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="loadStageContent()">
                            <i class="fas fa-download me-2"></i>Load Content
                        </button>
                        <button class="btn btn-success" onclick="saveStageContent()">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                        <button class="btn btn-info" onclick="exportContent()">
                            <i class="fas fa-file-export me-2"></i>Export
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Editor -->
        <div class="row">
            <div class="col-lg-8">
                <!-- Language Tabs -->
                <ul class="nav nav-tabs language-tabs" id="languageTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="english-tab" data-bs-toggle="tab" data-bs-target="#english-content" type="button" role="tab">
                            <i class="fas fa-flag-usa me-2"></i>English
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="arabic-tab" data-bs-toggle="tab" data-bs-target="#arabic-content" type="button" role="tab">
                            <i class="fas fa-flag me-2"></i>العربية
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="languageTabContent">
                    <!-- English Content -->
                    <div class="tab-pane fade show active" id="english-content" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-edit me-2"></i>English Content Editor</h5>
                            </div>
                            <div class="card-body">
                                <div id="englishSections">
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                                        <p>Select a stage to load content for editing</p>
                                    </div>
                                </div>
                                <button class="btn btn-outline-primary" onclick="addSection('en')">
                                    <i class="fas fa-plus me-2"></i>Add Section
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="tab-pane fade" id="arabic-content" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-edit me-2"></i>محرر المحتوى العربي</h5>
                            </div>
                            <div class="card-body" dir="rtl">
                                <div id="arabicSections">
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                                        <p>اختر مرحلة لتحميل المحتوى للتحرير</p>
                                    </div>
                                </div>
                                <button class="btn btn-outline-primary" onclick="addSection('ar')">
                                    <i class="fas fa-plus me-2"></i>إضافة قسم
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview Panel -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-eye me-2"></i>Live Preview</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="previewContent('en')">EN</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="previewContent('ar')">ع</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="contentPreview" class="content-preview">
                            <div class="text-center text-muted">
                                <i class="fas fa-eye-slash fa-2x mb-2"></i>
                                <p>Preview will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Statistics -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-bar me-2"></i>Content Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div id="contentStats">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="stat-item">
                                        <h6 id="englishSectionCount">0</h6>
                                        <small>English Sections</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-item">
                                        <h6 id="arabicSectionCount">0</h6>
                                        <small>Arabic Sections</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-info btn-sm" onclick="validateContent()">
                                <i class="fas fa-check-circle me-2"></i>Validate Content
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="generatePreview()">
                                <i class="fas fa-magic me-2"></i>Generate Preview
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="publishContent()">
                                <i class="fas fa-upload me-2"></i>Publish Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import/Export Tools -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exchange-alt me-2"></i>Import/Export Tools</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Import Content</h6>
                                <div class="mb-3">
                                    <label class="form-label">Upload JSON File:</label>
                                    <input type="file" class="form-control" id="importFile" accept=".json">
                                </div>
                                <button class="btn btn-primary" onclick="importContent()">
                                    <i class="fas fa-file-import me-2"></i>Import
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>Export Options</h6>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-secondary" onclick="exportAsJSON()">
                                        <i class="fas fa-file-code me-2"></i>Export as JSON
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="exportAsHTML()">
                                        <i class="fas fa-file-code me-2"></i>Export as HTML
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="exportAsMarkdown()">
                                        <i class="fas fa-file-alt me-2"></i>Export as Markdown
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/i18n.js"></script>
    <script src="js/content-store.js"></script>
    <script src="js/i18n-advanced.js"></script>
    
    <script>
        // Content Management System
        let currentStage = '';
        let contentData = { en: [], ar: [] };
        
        // Load stage content
        function loadStageContent() {
            const stageKey = document.getElementById('stageSelector').value;
            if (!stageKey) {
                alert('Please select a stage first');
                return;
            }
            
            currentStage = stageKey;
            
            // Load from content store
            if (window.ContentStore) {
                contentData.en = window.ContentStore.en[stageKey] || [];
                contentData.ar = window.ContentStore.ar[stageKey] || [];
                
                renderSections('en');
                renderSections('ar');
                updateStats();
                
                showNotification('Content loaded successfully', 'success');
            } else {
                showNotification('Content store not available', 'error');
            }
        }
        
        // Render sections for a language
        function renderSections(lang) {
            const container = document.getElementById(lang === 'en' ? 'englishSections' : 'arabicSections');
            const sections = contentData[lang] || [];
            
            if (sections.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <p>${lang === 'en' ? 'No content available' : 'لا يوجد محتوى متاح'}</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            sections.forEach((section, index) => {
                html += `
                    <div class="section-item" data-index="${index}">
                        <div class="section-header">
                            <h6>${lang === 'en' ? 'Section' : 'القسم'} ${index + 1}</h6>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeSection('${lang}', ${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">${lang === 'en' ? 'Title:' : 'العنوان:'}</label>
                            <input type="text" class="form-control" value="${section.title || ''}" 
                                   onchange="updateSectionTitle('${lang}', ${index}, this.value)">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">${lang === 'en' ? 'Content:' : 'المحتوى:'}</label>
                            <textarea class="form-control" rows="8" 
                                      onchange="updateSectionContent('${lang}', ${index}, this.value)">${section.content || ''}</textarea>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Add new section
        function addSection(lang) {
            if (!contentData[lang]) {
                contentData[lang] = [];
            }
            
            contentData[lang].push({
                title: lang === 'en' ? 'New Section' : 'قسم جديد',
                content: lang === 'en' ? '<p>Enter content here...</p>' : '<p>أدخل المحتوى هنا...</p>'
            });
            
            renderSections(lang);
            updateStats();
        }
        
        // Remove section
        function removeSection(lang, index) {
            if (confirm(lang === 'en' ? 'Remove this section?' : 'حذف هذا القسم؟')) {
                contentData[lang].splice(index, 1);
                renderSections(lang);
                updateStats();
            }
        }
        
        // Update section title
        function updateSectionTitle(lang, index, title) {
            if (contentData[lang] && contentData[lang][index]) {
                contentData[lang][index].title = title;
            }
        }
        
        // Update section content
        function updateSectionContent(lang, index, content) {
            if (contentData[lang] && contentData[lang][index]) {
                contentData[lang][index].content = content;
            }
        }
        
        // Update statistics
        function updateStats() {
            document.getElementById('englishSectionCount').textContent = contentData.en.length;
            document.getElementById('arabicSectionCount').textContent = contentData.ar.length;
        }
        
        // Preview content
        function previewContent(lang) {
            const preview = document.getElementById('contentPreview');
            const sections = contentData[lang] || [];
            
            if (sections.length === 0) {
                preview.innerHTML = `<p class="text-muted">${lang === 'en' ? 'No content to preview' : 'لا يوجد محتوى للمعاينة'}</p>`;
                return;
            }
            
            let html = '<div class="accordion" id="previewAccordion">';
            sections.forEach((section, index) => {
                html += `
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#preview-${index}">
                                ${section.title}
                            </button>
                        </h2>
                        <div id="preview-${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}">
                            <div class="accordion-body">
                                ${section.content}
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            preview.innerHTML = html;
            preview.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');
        }
        
        // Save content
        function saveStageContent() {
            if (!currentStage) {
                showNotification('No stage selected', 'warning');
                return;
            }
            
            // In a real implementation, this would save to a backend
            console.log('Saving content for stage:', currentStage, contentData);
            showNotification('Content saved successfully!', 'success');
        }
        
        // Export functions
        function exportAsJSON() {
            const data = {
                stage: currentStage,
                content: contentData,
                timestamp: new Date().toISOString()
            };
            
            downloadFile(JSON.stringify(data, null, 2), `${currentStage}_content.json`, 'application/json');
        }
        
        function exportAsHTML() {
            // Generate HTML export
            showNotification('HTML export feature coming soon', 'info');
        }
        
        function exportAsMarkdown() {
            // Generate Markdown export
            showNotification('Markdown export feature coming soon', 'info');
        }
        
        // Utility functions
        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function showNotification(message, type) {
            // Simple notification system
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Validation and publishing functions
        function validateContent() {
            let issues = [];
            
            ['en', 'ar'].forEach(lang => {
                const sections = contentData[lang] || [];
                sections.forEach((section, index) => {
                    if (!section.title || section.title.trim() === '') {
                        issues.push(`${lang.toUpperCase()} Section ${index + 1}: Missing title`);
                    }
                    if (!section.content || section.content.trim() === '') {
                        issues.push(`${lang.toUpperCase()} Section ${index + 1}: Missing content`);
                    }
                });
            });
            
            if (issues.length === 0) {
                showNotification('Content validation passed!', 'success');
            } else {
                showNotification(`Validation issues found: ${issues.length}`, 'warning');
                console.log('Validation issues:', issues);
            }
        }
        
        function generatePreview() {
            const activeTab = document.querySelector('.nav-link.active').id;
            const lang = activeTab === 'english-tab' ? 'en' : 'ar';
            previewContent(lang);
            showNotification('Preview generated', 'info');
        }
        
        function publishContent() {
            validateContent();
            // In a real implementation, this would publish to production
            showNotification('Content published successfully!', 'success');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
        });
    </script>
</body>
</html>
