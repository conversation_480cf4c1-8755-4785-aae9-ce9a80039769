import React from 'react';
import { Chapter } from '../types';
import { Clock, BookOpen, Users, MessageCircle, ExternalLink } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface ChapterContentProps {
  chapter: Chapter;
}

const ChapterContent: React.FC<ChapterContentProps> = ({ chapter }) => {
  const { language, t } = useLanguage();

  const getExerciseTypeLabel = (type: string) => {
    const labels = {
      ar: {
        discussion: 'نقاش',
        practical: 'تطبيقي',
        research: 'بحثي'
      },
      en: {
        discussion: 'Discussion',
        practical: 'Practical',
        research: 'Research'
      }
    };
    return labels[language][type as keyof typeof labels['ar']] || type;
  };

  const getReferenceTypeLabel = (type: string) => {
    const labels = {
      ar: {
        journal: 'مجلة علمية',
        book: 'كتاب',
        website: 'موقع',
        patent: 'براءة اختراع'
      },
      en: {
        journal: 'Journal',
        book: 'Book',
        website: 'Website',
        patent: 'Patent'
      }
    };
    return labels[language][type as keyof typeof labels['ar']] || type;
  };

  const getExerciseTypeColor = (type: string) => {
    const colors = {
      discussion: 'bg-blue-100 text-blue-700',
      practical: 'bg-purple-100 text-purple-700',
      research: 'bg-orange-100 text-orange-700'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-700';
  };

  const getReferenceTypeColor = (type: string) => {
    const colors = {
      journal: 'bg-blue-100 text-blue-700',
      book: 'bg-green-100 text-green-700',
      website: 'bg-purple-100 text-purple-700',
      patent: 'bg-orange-100 text-orange-700'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-700';
  };

  return (
    <div className="max-w-4xl mx-auto" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Chapter Header */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-8 rounded-xl shadow-lg">
          <div className={`flex items-center ${language === 'ar' ? 'space-x-4 space-x-reverse' : 'space-x-4'} mb-4`}>
            <BookOpen className="h-8 w-8 text-blue-100" />
            <div>
              <h1 className="text-3xl font-bold mb-2">
                {language === 'ar' ? chapter.arabicTitle : chapter.title}
              </h1>
              <p className="text-blue-100 text-lg">
                {language === 'ar' ? chapter.title : chapter.arabicTitle}
              </p>
            </div>
          </div>
          
          <div className={`flex items-center ${language === 'ar' ? 'space-x-6 space-x-reverse' : 'space-x-6'} text-blue-100 text-sm`}>
            <div className={`flex items-center ${language === 'ar' ? 'space-x-2 space-x-reverse' : 'space-x-2'}`}>
              <Clock className="h-4 w-4" />
              <span>{t('reading_time')}</span>
            </div>
            <div className={`flex items-center ${language === 'ar' ? 'space-x-2 space-x-reverse' : 'space-x-2'}`}>
              <Users className="h-4 w-4" />
              <span>{t('level_intermediate')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Chapter Content */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
        <div 
          className="prose prose-lg max-w-none"
          style={{
            direction: language === 'ar' ? 'rtl' : 'ltr',
            lineHeight: '1.8',
            fontSize: '1.1rem'
          }}
          dangerouslySetInnerHTML={{ 
            __html: language === 'ar' ? chapter.arabicContent : chapter.content 
          }}
        />
      </div>

      {/* Exercises Section */}
      {chapter.exercises && chapter.exercises.length > 0 && (
        <div className="bg-green-50 rounded-xl p-6 mb-8 border border-green-200">
          <h3 className={`text-xl font-semibold text-green-800 mb-4 flex items-center ${language === 'ar' ? 'space-x-2 space-x-reverse' : 'space-x-2'}`}>
            <MessageCircle className="h-6 w-6" />
            <span>{t('exercises_discussion')}</span>
          </h3>
          <div className="space-y-4">
            {chapter.exercises.map((exercise, index) => (
              <div key={exercise.id} className="bg-white rounded-lg p-4 border border-green-200">
                <div className={`flex items-start ${language === 'ar' ? 'space-x-3 space-x-reverse' : 'space-x-3'}`}>
                  <div className="bg-green-100 text-green-700 rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold flex-shrink-0">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <div className={`flex items-center ${language === 'ar' ? 'space-x-2 space-x-reverse' : 'space-x-2'} mb-2`}>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getExerciseTypeColor(exercise.type)}`}>
                        {getExerciseTypeLabel(exercise.type)}
                      </span>
                    </div>
                    <p className={`text-gray-700 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      {language === 'ar' ? exercise.arabicQuestion : exercise.question}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* References Section */}
      {chapter.references && chapter.references.length > 0 && (
        <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
          <h3 className={`text-xl font-semibold text-gray-800 mb-4 flex items-center ${language === 'ar' ? 'space-x-2 space-x-reverse' : 'space-x-2'}`}>
            <ExternalLink className="h-6 w-6" />
            <span>{t('references_sources')}</span>
          </h3>
          <div className="space-y-3">
            {chapter.references.map((ref, index) => (
              <div key={ref.id} className="bg-white rounded-lg p-4 border border-gray-200">
                <div className={`flex items-start ${language === 'ar' ? 'space-x-3 space-x-reverse' : 'space-x-3'}`}>
                  <div className="bg-gray-100 text-gray-700 rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold flex-shrink-0">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <div className={`flex items-center ${language === 'ar' ? 'space-x-2 space-x-reverse' : 'space-x-2'} mb-2`}>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getReferenceTypeColor(ref.type)}`}>
                        {getReferenceTypeLabel(ref.type)}
                      </span>
                    </div>
                    <p className={`text-gray-700 ${language === 'ar' ? 'text-right' : 'text-left'} text-sm leading-relaxed`}>
                      {language === 'ar' ? ref.arabicCitation : ref.citation}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ChapterContent;